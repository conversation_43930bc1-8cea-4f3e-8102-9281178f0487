import { NextRequest } from 'next/server'
import { withAuth, withAuthAndPermission, createApiResponse, handleApiError } from '@/lib/api-utils'
import type { Tables, TablesInsert } from '@/types/supabase'

type Appointment = Tables<'appointments'>
type AppointmentInsert = TablesInsert<'appointments'>

export async function GET(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const { searchParams } = new URL(request.url)
      const date = searchParams.get('date')
      const patientId = searchParams.get('patient_id')

      let query = supabase
        .from('appointments')
        .select(`
          *,
          patients!inner(name),
          healthcare_professionals(name, specialty)
        `)
        .eq('user_id', userId)

      if (date) {
        // Parse date in local timezone to avoid UTC conversion issues
        const [year, month, day] = date.split('-').map(Number)
        const startDate = new Date(year, month - 1, day, 0, 0, 0, 0)
        const endDate = new Date(year, month - 1, day + 1, 0, 0, 0, 0)

        // Convert to ISO string but maintain local timezone context
        // This ensures we filter correctly for Brazilian timezone
        query = query
          .gte('start_time', startDate.toISOString())
          .lt('start_time', endDate.toISOString())
      }

      if (patientId) {
        query = query.eq('patient_id', patientId)
      }

      const { data: appointments, error } = await query.order('start_time')

      if (error) {
        return handleApiError(error)
      }

      return createApiResponse(appointments)
    } catch (error) {
      return handleApiError(error)
    }
  })
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (userId, supabase) => {
    try {
      const body = await request.json()
      const { procedures = [], ...appointmentBody } = body

      const appointmentData: AppointmentInsert = {
        ...appointmentBody,
        user_id: userId,
        status: body.status || 'scheduled',
        start_time: new Date(body.start_time).toISOString(),
        end_time: new Date(body.end_time).toISOString(),
        healthcare_professional_id: body.healthcare_professional_id || null,
        total_price: body.total_price || null,
        recurrence_rule: body.has_recurrence ? JSON.stringify({
          type: body.recurrence_type,
          interval: body.recurrence_interval,
          days: body.recurrence_days,
          end_type: body.recurrence_end_type,
          end_date: body.recurrence_end_date,
          count: body.recurrence_count
        }) : null,
        recurrence_end_date: body.recurrence_end_date || null
      }

      // Create appointment
      const { data: appointment, error: appointmentError } = await supabase
        .from('appointments')
        .insert(appointmentData)
        .select(`
          *,
          patients!inner(name),
          healthcare_professionals(name, specialty)
        `)
        .single()

      if (appointmentError) {
        return handleApiError(appointmentError)
      }

      // Create appointment procedures if any
      if (procedures.length > 0) {
        const procedureInserts = procedures.map((proc: any) => ({
          appointment_id: appointment.id,
          procedure_id: proc.procedure_id,
          quantity: parseInt(proc.quantity),
          unit_price: parseFloat(proc.unit_price),
          total_price: parseFloat(proc.total_price)
        }))

        const { error: proceduresError } = await supabase
          .from('appointment_procedures')
          .insert(procedureInserts)

        if (proceduresError) {
          // Rollback appointment if procedures fail
          await supabase.from('appointments').delete().eq('id', appointment.id)
          return handleApiError(proceduresError)
        }
      }

      return createApiResponse(appointment, undefined, 201)
    } catch (error) {
      return handleApiError(error)
    }
  })
}
