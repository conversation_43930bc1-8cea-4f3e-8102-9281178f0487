/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/medical-records/route";
exports.ids = ["app/api/medical-records/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedical-records%2Froute&page=%2Fapi%2Fmedical-records%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedical-records%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedical-records%2Froute&page=%2Fapi%2Fmedical-records%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedical-records%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_medical_records_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/medical-records/route.ts */ \"(rsc)/./src/app/api/medical-records/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/medical-records/route\",\n        pathname: \"/api/medical-records\",\n        filename: \"route\",\n        bundlePath: \"app/api/medical-records/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\api\\\\medical-records\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_cirov_Documents_next_js_nectar_nectar_nextjs_src_app_api_medical_records_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedical-records%2Froute&page=%2Fapi%2Fmedical-records%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedical-records%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/medical-records/route.ts":
/*!**********************************************!*\
  !*** ./src/app/api/medical-records/route.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DELETE: () => (/* binding */ DELETE),\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST),\n/* harmony export */   PUT: () => (/* binding */ PUT)\n/* harmony export */ });\n/* harmony import */ var _lib_api_utils__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @/lib/api-utils */ \"(rsc)/./src/lib/api-utils.ts\");\n/* harmony import */ var _lib_encryption__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/encryption */ \"(rsc)/./src/lib/encryption.ts\");\n\n\nasync function GET(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const { searchParams } = new URL(request.url);\n            const appointmentId = searchParams.get('appointment_id');\n            const patientId = searchParams.get('patient_id');\n            let query = supabase.from('medical_records').select(`\n          *,\n          appointments!inner(patient_id)\n        `).eq('user_id', userId).order('created_at', {\n                ascending: false\n            });\n            if (appointmentId) {\n                query = query.eq('appointment_id', appointmentId);\n            }\n            if (patientId) {\n                query = query.eq('appointments.patient_id', patientId);\n            }\n            const { data: records, error } = await query;\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            // Get user names for the records\n            const userIds = [\n                ...new Set(records?.map((r)=>r.user_id) || [])\n            ];\n            const { data: users } = await supabase.from('users').select('id, name').in('id', userIds);\n            const userMap = new Map(users?.map((u)=>[\n                    u.id,\n                    u.name\n                ]) || []);\n            // Format the response to include creator name and decrypt notes\n            const formattedRecords = records?.map((record)=>({\n                    ...record,\n                    notes: (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_1__.safeDecryptMedicalData)(record.notes),\n                    created_by_name: userMap.get(record.user_id) || 'Sistema'\n                })) || [];\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(formattedRecords);\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\nasync function POST(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const body = await request.json();\n            const { appointment_id, patient_id, notes, is_draft = false } = body;\n            if (!appointment_id || !notes?.trim()) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(new Error('appointment_id and notes are required'));\n            }\n            // Verify the appointment belongs to the user\n            const { data: appointment, error: appointmentError } = await supabase.from('appointments').select('id, patient_id').eq('id', appointment_id).eq('user_id', userId).single();\n            if (appointmentError || !appointment) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(new Error('Appointment not found or access denied'));\n            }\n            const recordData = {\n                appointment_id,\n                patient_id: patient_id || appointment.patient_id,\n                user_id: userId,\n                notes: (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_1__.safeEncryptMedicalData)(notes.trim()),\n                is_draft: Boolean(is_draft),\n                created_at: new Date().toISOString()\n            };\n            const { data: record, error } = await supabase.from('medical_records').insert(recordData).select('*').single();\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            // Get user name\n            const { data: user } = await supabase.from('users').select('name').eq('id', record.user_id).single();\n            // Format the response and decrypt notes\n            const formattedRecord = {\n                ...record,\n                notes: (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_1__.safeDecryptMedicalData)(record.notes),\n                created_by_name: user?.name || 'Sistema'\n            };\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(formattedRecord, undefined, 201);\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\nasync function PUT(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const body = await request.json();\n            const { id, notes } = body;\n            if (!id || !notes?.trim()) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(new Error('id and notes are required'));\n            }\n            const { data: record, error } = await supabase.from('medical_records').update({\n                notes: (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_1__.safeEncryptMedicalData)(notes.trim()),\n                updated_at: new Date().toISOString()\n            }).eq('id', id).eq('user_id', userId).select('*').single();\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            // Get user name\n            const { data: user } = await supabase.from('users').select('name').eq('id', record.user_id).single();\n            // Format the response and decrypt notes\n            const formattedRecord = {\n                ...record,\n                notes: (0,_lib_encryption__WEBPACK_IMPORTED_MODULE_1__.safeDecryptMedicalData)(record.notes),\n                created_by_name: user?.name || 'Sistema'\n            };\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)(formattedRecord);\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\nasync function DELETE(request) {\n    return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.withAuth)(request, async (userId, supabase)=>{\n        try {\n            const { searchParams } = new URL(request.url);\n            const id = searchParams.get('id');\n            if (!id) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(new Error('id is required'));\n            }\n            const { error } = await supabase.from('medical_records').delete().eq('id', id).eq('user_id', userId);\n            if (error) {\n                return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n            }\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.createApiResponse)({\n                message: 'Medical record deleted successfully'\n            });\n        } catch (error) {\n            return (0,_lib_api_utils__WEBPACK_IMPORTED_MODULE_0__.handleApiError)(error);\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/medical-records/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/api-utils.ts":
/*!******************************!*\
  !*** ./src/lib/api-utils.ts ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createApiResponse: () => (/* binding */ createApiResponse),\n/* harmony export */   handleApiError: () => (/* binding */ handleApiError),\n/* harmony export */   withAuth: () => (/* binding */ withAuth),\n/* harmony export */   withAuthAndPermission: () => (/* binding */ withAuthAndPermission)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase/server */ \"(rsc)/./src/lib/supabase/server.ts\");\n/* harmony import */ var _lib_permissions__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/permissions */ \"(rsc)/./src/lib/permissions.ts\");\n\n\n\nfunction createApiResponse(data, message, status = 200) {\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        data,\n        message\n    }, {\n        status\n    });\n}\nasync function withAuth(request, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'Unauthorized'\n            }, {\n                status: 401\n            });\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: error instanceof Error ? error.message : 'Internal server error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function withAuthAndPermission(request, resource, action, handler) {\n    try {\n        const supabase = await (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createClient)();\n        const { data: { user }, error: authError } = await supabase.auth.getUser();\n        if (authError || !user) {\n            return createApiResponse(undefined, 'Unauthorized', 401);\n        }\n        // Check permissions\n        const hasAccess = await (0,_lib_permissions__WEBPACK_IMPORTED_MODULE_2__.hasPermission)(user.id, resource, action);\n        if (!hasAccess) {\n            return createApiResponse(undefined, 'Forbidden: Insufficient permissions', 403);\n        }\n        return await handler(user.id, supabase);\n    } catch (error) {\n        console.error('API Error:', error);\n        return createApiResponse(undefined, error instanceof Error ? error.message : 'Internal server error', 500);\n    }\n}\nfunction handleApiError(error) {\n    console.error('API Error:', error);\n    if (error?.code === 'PGRST116') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource not found'\n        }, {\n            status: 404\n        });\n    }\n    if (error?.code === '23505') {\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Resource already exists'\n        }, {\n            status: 409\n        });\n    }\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n        error: error?.message || 'Internal server error'\n    }, {\n        status: 500\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/api-utils.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/encryption.ts":
/*!*******************************!*\
  !*** ./src/lib/encryption.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   decryptMedicalData: () => (/* binding */ decryptMedicalData),\n/* harmony export */   encryptMedicalData: () => (/* binding */ encryptMedicalData),\n/* harmony export */   generateEncryptionKey: () => (/* binding */ generateEncryptionKey),\n/* harmony export */   isEncrypted: () => (/* binding */ isEncrypted),\n/* harmony export */   safeDecryptMedicalData: () => (/* binding */ safeDecryptMedicalData),\n/* harmony export */   safeEncryptMedicalData: () => (/* binding */ safeEncryptMedicalData)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\n/**\n * Medical data encryption utility\n * Uses AES-256-GCM for secure encryption of sensitive medical records\n */ const ALGORITHM = 'aes-256-gcm';\nconst KEY_LENGTH = 32; // 256 bits\nconst IV_LENGTH = 16; // 128 bits\nconst TAG_LENGTH = 16; // 128 bits\n/**\n * Get encryption key from environment variable\n * In production, this should be stored securely (e.g., AWS KMS, Azure Key Vault)\n */ function getEncryptionKey() {\n    const key = process.env.MEDICAL_RECORDS_ENCRYPTION_KEY;\n    if (!key) {\n        throw new Error('MEDICAL_RECORDS_ENCRYPTION_KEY environment variable is required');\n    }\n    // If key is hex string, convert to buffer\n    if (key.length === 64) {\n        return Buffer.from(key, 'hex');\n    }\n    // If key is base64, convert to buffer\n    if (key.length === 44 && key.endsWith('=')) {\n        return Buffer.from(key, 'base64');\n    }\n    // Otherwise, hash the key to ensure it's 32 bytes\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(key).digest();\n}\n/**\n * Generate a random encryption key (for initial setup)\n */ function generateEncryptionKey() {\n    return crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(KEY_LENGTH).toString('hex');\n}\n/**\n * Encrypt sensitive medical data\n */ function encryptMedicalData(plaintext) {\n    try {\n        const key = getEncryptionKey();\n        const iv = crypto__WEBPACK_IMPORTED_MODULE_0___default().randomBytes(IV_LENGTH);\n        const cipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createCipheriv(ALGORITHM, key, iv);\n        cipher.setAAD(Buffer.from('medical-record', 'utf8')); // Additional authenticated data\n        let encrypted = cipher.update(plaintext, 'utf8', 'hex');\n        encrypted += cipher.final('hex');\n        const tag = cipher.getAuthTag();\n        // Combine IV + tag + encrypted data\n        const combined = Buffer.concat([\n            iv,\n            tag,\n            Buffer.from(encrypted, 'hex')\n        ]);\n        return combined.toString('base64');\n    } catch (error) {\n        console.error('Encryption error:', error);\n        throw new Error('Failed to encrypt medical data');\n    }\n}\n/**\n * Decrypt sensitive medical data\n */ function decryptMedicalData(encryptedData) {\n    try {\n        const key = getEncryptionKey();\n        const combined = Buffer.from(encryptedData, 'base64');\n        // Extract IV, tag, and encrypted data\n        const iv = combined.subarray(0, IV_LENGTH);\n        const tag = combined.subarray(IV_LENGTH, IV_LENGTH + TAG_LENGTH);\n        const encrypted = combined.subarray(IV_LENGTH + TAG_LENGTH);\n        const decipher = crypto__WEBPACK_IMPORTED_MODULE_0___default().createDecipheriv(ALGORITHM, key, iv);\n        decipher.setAuthTag(tag);\n        decipher.setAAD(Buffer.from('medical-record', 'utf8'));\n        let decrypted = decipher.update(encrypted, undefined, 'utf8');\n        decrypted += decipher.final('utf8');\n        return decrypted;\n    } catch (error) {\n        console.error('Decryption error:', error);\n        throw new Error('Failed to decrypt medical data');\n    }\n}\n/**\n * Validate if data is encrypted (basic check)\n */ function isEncrypted(data) {\n    try {\n        // Check if it's a valid base64 string with expected length\n        const buffer = Buffer.from(data, 'base64');\n        return buffer.length > IV_LENGTH + TAG_LENGTH; // IV + tag + encrypted data for GCM mode\n    } catch  {\n        return false;\n    }\n}\n/**\n * Safely encrypt medical data with error handling\n */ function safeEncryptMedicalData(plaintext) {\n    if (!plaintext || plaintext.trim() === '') {\n        return plaintext;\n    }\n    try {\n        return encryptMedicalData(plaintext);\n    } catch (error) {\n        console.error('Safe encryption failed, storing as plaintext:', error);\n        // In production, you might want to fail here instead of storing plaintext\n        return plaintext;\n    }\n}\n/**\n * Safely decrypt medical data with error handling\n */ function safeDecryptMedicalData(encryptedData) {\n    if (!encryptedData || encryptedData.trim() === '') {\n        return encryptedData;\n    }\n    // If data doesn't look encrypted, return as-is (for backward compatibility)\n    if (!isEncrypted(encryptedData)) {\n        return encryptedData;\n    }\n    try {\n        return decryptMedicalData(encryptedData);\n    } catch (error) {\n        console.error('Safe decryption failed:', error);\n        // Return a placeholder or the encrypted data (depending on your security policy)\n        return '[ENCRYPTED DATA - DECRYPTION FAILED]';\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/encryption.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/permissions.ts":
/*!********************************!*\
  !*** ./src/lib/permissions.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DEFAULT_PERMISSIONS: () => (/* binding */ DEFAULT_PERMISSIONS),\n/* harmony export */   getUserPermissions: () => (/* binding */ getUserPermissions),\n/* harmony export */   hasPermission: () => (/* binding */ hasPermission),\n/* harmony export */   hasRole: () => (/* binding */ hasRole),\n/* harmony export */   isAdmin: () => (/* binding */ isAdmin),\n/* harmony export */   requirePermission: () => (/* binding */ requirePermission),\n/* harmony export */   requireRole: () => (/* binding */ requireRole)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\nconst supabaseUrl = \"https://zmwdnemlzndjavlriyrc.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\n// Create a Supabase client with service role key for server-side operations\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseServiceKey);\n/**\n * Check if a user has a specific permission\n */ async function hasPermission(userId, resource, action) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return false;\n        }\n        // Check if any of the user's roles have the required permission\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('*').in('role_name', roleNames).eq('resource', resource).eq('action', action);\n        if (permissionsError) {\n            console.error('Error checking permissions:', permissionsError);\n            return false;\n        }\n        return permissions && permissions.length > 0;\n    } catch (error) {\n        console.error('Error in hasPermission:', error);\n        return false;\n    }\n}\n/**\n * Check if a user has any of the specified roles\n */ async function hasRole(userId, roles) {\n    try {\n        const { data: userRoles, error } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId).in('role_name', roles);\n        if (error) {\n            console.error('Error checking roles:', error);\n            return false;\n        }\n        return userRoles && userRoles.length > 0;\n    } catch (error) {\n        console.error('Error in hasRole:', error);\n        return false;\n    }\n}\n/**\n * Get all permissions for a user\n */ async function getUserPermissions(userId) {\n    try {\n        // Get user roles\n        const { data: userRoles, error: rolesError } = await supabaseAdmin.from('user_roles').select('role_name').eq('user_id', userId);\n        if (rolesError || !userRoles || userRoles.length === 0) {\n            return [];\n        }\n        // Get all permissions for these roles\n        const roleNames = userRoles.map((role)=>role.role_name);\n        const { data: permissions, error: permissionsError } = await supabaseAdmin.from('permissions').select('resource, action').in('role_name', roleNames);\n        if (permissionsError) {\n            console.error('Error getting user permissions:', permissionsError);\n            return [];\n        }\n        return permissions || [];\n    } catch (error) {\n        console.error('Error in getUserPermissions:', error);\n        return [];\n    }\n}\n/**\n * Check if a user is an admin\n */ async function isAdmin(userId) {\n    return hasRole(userId, [\n        'admin'\n    ]);\n}\n/**\n * Middleware function to check permissions for API routes\n */ function requirePermission(resource, action) {\n    return async (userId)=>{\n        return hasPermission(userId, resource, action);\n    };\n}\n/**\n * Middleware function to check roles for API routes\n */ function requireRole(roles) {\n    return async (userId)=>{\n        return hasRole(userId, roles);\n    };\n}\n/**\n * Default permissions for each role\n */ const DEFAULT_PERMISSIONS = {\n    admin: [\n        // Full access to everything\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'appointments',\n            action: 'delete'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'delete'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'delete'\n        },\n        {\n            resource: 'procedures',\n            action: 'create'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        },\n        {\n            resource: 'procedures',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'delete'\n        },\n        {\n            resource: 'settings',\n            action: 'read'\n        },\n        {\n            resource: 'settings',\n            action: 'update'\n        }\n    ],\n    doctor: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'medical_records',\n            action: 'create'\n        },\n        {\n            resource: 'medical_records',\n            action: 'read'\n        },\n        {\n            resource: 'medical_records',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    secretary: [\n        // Can manage appointments and patients, read procedures\n        {\n            resource: 'appointments',\n            action: 'create'\n        },\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'appointments',\n            action: 'update'\n        },\n        {\n            resource: 'patients',\n            action: 'create'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'update'\n        },\n        {\n            resource: 'procedures',\n            action: 'read'\n        }\n    ],\n    assistant: [\n        // Read-only access to appointments and patients\n        {\n            resource: 'appointments',\n            action: 'read'\n        },\n        {\n            resource: 'patients',\n            action: 'read'\n        }\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/permissions.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase/server.ts":
/*!************************************!*\
  !*** ./src/lib/supabase/server.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createClient: () => (/* binding */ createClient)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/dist/api/headers.js\");\n\n\nasync function createClient() {\n    const cookieStore = await (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zmwdnemlzndjavlriyrc.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inptd2RuZW1sem5kamF2bHJpeXJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE4MTE5NTksImV4cCI6MjA2NzM4Nzk1OX0.XNRQjZmMZ7s4aKrJVSQFlu9ASryGJc5fBX6iNnjOPEM\", {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase/server.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fmedical-records%2Froute&page=%2Fapi%2Fmedical-records%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fmedical-records%2Froute.ts&appDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Ccirov%5CDocuments%5Cnext-js%5Cnectar%5Cnectar-nextjs&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();