import { cn } from "@/lib/utils"
import { Skeleton } from "./skeleton"

// Patient List Skeleton
export function PatientListSkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="flex items-center space-x-4 p-4 border rounded-lg">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div className="space-y-2 flex-1">
            <Skeleton className="h-4 w-[200px]" />
            <Skeleton className="h-3 w-[150px]" />
          </div>
          <div className="space-y-2">
            <Skeleton className="h-3 w-[100px]" />
            <Skeleton className="h-3 w-[80px]" />
          </div>
          <Skeleton className="h-8 w-[60px]" />
        </div>
      ))}
    </div>
  )
}

// Patient Table Skeleton
export function PatientTableSkeleton({ rows = 5 }: { rows?: number }) {
  return (
    <div className="space-y-3">
      {/* Table Header */}
      <div className="flex space-x-4 p-3 border-b">
        <Skeleton className="h-4 w-[120px]" />
        <Skeleton className="h-4 w-[100px]" />
        <Skeleton className="h-4 w-[140px]" />
        <Skeleton className="h-4 w-[120px]" />
        <Skeleton className="h-4 w-[80px]" />
      </div>
      
      {/* Table Rows */}
      {Array.from({ length: rows }).map((_, i) => (
        <div key={i} className="flex space-x-4 p-3 border-b">
          <div className="space-y-1">
            <Skeleton className="h-4 w-[120px]" />
            <Skeleton className="h-3 w-[100px]" />
          </div>
          <div className="space-y-1">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-3 w-[80px]" />
          </div>
          <Skeleton className="h-4 w-[140px]" />
          <Skeleton className="h-4 w-[120px]" />
          <Skeleton className="h-8 w-[80px] rounded-md" />
        </div>
      ))}
    </div>
  )
}

// Appointment Card Skeleton
export function AppointmentCardSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="p-4 border rounded-lg space-y-3">
          <div className="flex justify-between items-start">
            <div className="space-y-2 flex-1">
              <Skeleton className="h-5 w-[180px]" />
              <Skeleton className="h-4 w-[140px]" />
              <Skeleton className="h-3 w-[100px]" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-[80px] rounded-full" />
              <Skeleton className="h-8 w-[60px] rounded-md" />
            </div>
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-3 w-[60px]" />
            <Skeleton className="h-3 w-[80px]" />
          </div>
        </div>
      ))}
    </div>
  )
}

// Calendar Skeleton
export function CalendarSkeleton() {
  return (
    <div className="space-y-4">
      {/* Calendar Header */}
      <div className="flex justify-between items-center p-4 border-b">
        <Skeleton className="h-8 w-[200px]" />
        <div className="flex space-x-2">
          <Skeleton className="h-8 w-[80px]" />
          <Skeleton className="h-8 w-[80px]" />
          <Skeleton className="h-8 w-[80px]" />
        </div>
      </div>
      
      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-2 p-4">
        {/* Week days */}
        {Array.from({ length: 7 }).map((_, i) => (
          <Skeleton key={`day-${i}`} className="h-6 w-full" />
        ))}
        
        {/* Calendar cells */}
        {Array.from({ length: 35 }).map((_, i) => (
          <div key={`cell-${i}`} className="space-y-1 p-2 min-h-[100px] border rounded">
            <Skeleton className="h-4 w-6" />
            {Math.random() > 0.7 && (
              <div className="space-y-1">
                <Skeleton className="h-3 w-full" />
                {Math.random() > 0.5 && <Skeleton className="h-3 w-3/4" />}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

// Dashboard Stats Skeleton
export function DashboardStatsSkeleton() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="p-6 border rounded-lg space-y-2">
          <div className="flex items-center justify-between">
            <Skeleton className="h-4 w-[100px]" />
            <Skeleton className="h-5 w-5 rounded" />
          </div>
          <Skeleton className="h-8 w-[60px]" />
          <Skeleton className="h-3 w-[120px]" />
        </div>
      ))}
    </div>
  )
}

// Medical Record Skeleton
export function MedicalRecordSkeleton() {
  return (
    <div className="space-y-6">
      {/* Patient Info */}
      <div className="p-4 border rounded-lg space-y-3">
        <div className="flex items-center space-x-3">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-5 w-[200px]" />
            <Skeleton className="h-4 w-[150px]" />
          </div>
        </div>
      </div>
      
      {/* Appointment Details */}
      <div className="grid md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <Skeleton className="h-4 w-[80px]" />
          <Skeleton className="h-6 w-[120px]" />
        </div>
        <div className="space-y-2">
          <Skeleton className="h-4 w-[60px]" />
          <Skeleton className="h-6 w-[100px] rounded-full" />
        </div>
      </div>
      
      {/* Medical Record Content */}
      <div className="space-y-4">
        <Skeleton className="h-4 w-[150px]" />
        <div className="space-y-2">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
      </div>
    </div>
  )
}

// Consultation History Skeleton
export function ConsultationHistorySkeleton({ count = 5 }: { count?: number }) {
  return (
    <div className="space-y-4">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="p-4 border rounded-lg space-y-3">
          <div className="flex justify-between items-start">
            <div className="space-y-2">
              <Skeleton className="h-5 w-[160px]" />
              <Skeleton className="h-4 w-[120px]" />
            </div>
            <Skeleton className="h-6 w-[80px] rounded-full" />
          </div>
          <div className="space-y-1">
            <Skeleton className="h-3 w-[200px]" />
            <Skeleton className="h-3 w-[180px]" />
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-[80px] rounded-md" />
            <Skeleton className="h-8 w-[100px] rounded-md" />
          </div>
        </div>
      ))}
    </div>
  )
}

// File Attachments Skeleton
export function FileAttachmentsSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      {Array.from({ length: count }).map((_, i) => (
        <div key={i} className="p-4 border rounded-lg space-y-3">
          <div className="flex items-center space-x-3">
            <Skeleton className="h-10 w-10 rounded" />
            <div className="space-y-1 flex-1">
              <Skeleton className="h-4 w-[120px]" />
              <Skeleton className="h-3 w-[80px]" />
            </div>
          </div>
          <div className="flex space-x-2">
            <Skeleton className="h-8 w-[60px] rounded-md" />
            <Skeleton className="h-8 w-[70px] rounded-md" />
          </div>
        </div>
      ))}
    </div>
  )
}

// Generic Loading Container
export function LoadingContainer({ 
  children, 
  loading, 
  skeleton 
}: { 
  children: React.ReactNode
  loading: boolean
  skeleton: React.ReactNode 
}) {
  if (loading) {
    return <>{skeleton}</>
  }
  
  return <>{children}</>
}

// Page Loading Skeleton
export function PageLoadingSkeleton() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="space-y-2">
        <Skeleton className="h-8 w-[200px]" />
        <Skeleton className="h-4 w-[300px]" />
      </div>
      
      {/* Action Buttons */}
      <div className="flex space-x-2">
        <Skeleton className="h-10 w-[120px] rounded-md" />
        <Skeleton className="h-10 w-[100px] rounded-md" />
      </div>
      
      {/* Main Content */}
      <div className="space-y-4">
        <Skeleton className="h-[400px] w-full rounded-lg" />
      </div>
    </div>
  )
}
