"use client"

import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useRouter } from 'next/navigation';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  User,
  Calendar,
  FileText,
  Edit,
  Save,
  X,
  Upload,
  Download,
  Trash2,
  Phone,
  Mail,
  MapPin,
  Clock,
  Plus,
  Stethoscope,
  ArrowLeft
} from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatDateBR, formatTimeBR, formatDateTimeBR, getAppointmentStatusBR, getAppointmentTypeBR } from '@/lib/date-utils';
import { makeAuthenticatedRequest } from '@/lib/api-client';

type Patient = {
  id: string;
  name: string;
  email: string | null;
  phone: string | null;
  birth_date: string | null;
  cpf: string | null;
  address: string | null;
  notes: string | null;
  created_at: string;
  updated_at: string;
};

type Appointment = {
  id: string;
  title: string;
  description: string | null;
  start_time: string;
  end_time: string;
  type: string;
  status: string;
  price: number | null;
  healthcare_professional_name?: string;
};

type PatientAttachment = {
  id: string;
  file_name: string;
  file_path: string;
  file_size: number | null;
  mime_type: string | null;
  created_at: string;
};

const PatientDetailPage = () => {
  const params = useParams();
  const router = useRouter();
  const { toast } = useToast();
  const patientId = params.id as string;

  const [patient, setPatient] = useState<Patient | null>(null);
  const [appointments, setAppointments] = useState<Appointment[]>([]);
  const [attachments, setAttachments] = useState<PatientAttachment[]>([]);
  const [loading, setLoading] = useState(true);
  const [editMode, setEditMode] = useState(false);
  const [uploadDialogOpen, setUploadDialogOpen] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [medicalRecordOpen, setMedicalRecordOpen] = useState(false);
  const [selectedAppointment, setSelectedAppointment] = useState<Appointment | null>(null);
  const [medicalRecords, setMedicalRecords] = useState<any[]>([]);
  const [newRecord, setNewRecord] = useState('');

  const [editForm, setEditForm] = useState({
    name: '',
    email: '',
    phone: '',
    birth_date: '',
    cpf: '',
    address: '',
    notes: ''
  });

  useEffect(() => {
    if (patientId) {
      fetchPatientData();
      fetchAppointments();
      fetchAttachments();
    }
  }, [patientId]);

  const fetchPatientData = async () => {
    try {
      const response = await makeAuthenticatedRequest(`/api/patients/${patientId}`);
      if (!response.ok) throw new Error('Failed to fetch patient');
      const result = await response.json();
      const data = result.data || result;
      setPatient(data);
      setEditForm({
        name: data.name || '',
        email: data.email || '',
        phone: data.phone || '',
        birth_date: data.birth_date || '',
        cpf: data.cpf || '',
        address: data.address || '',
        notes: data.notes || ''
      });
    } catch (error) {
      console.error('Error fetching patient:', error);
      toast({
        title: "Erro",
        description: "Erro ao carregar dados do paciente.",
        variant: "destructive"
      });
    }
  };

  const fetchAppointments = async () => {
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments?patient_id=${patientId}`);
      if (!response.ok) throw new Error('Failed to fetch appointments');
      const result = await response.json();
      const data = result.data || result;
      setAppointments(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching appointments:', error);
      setAppointments([]);
    }
  };

  const fetchAttachments = async () => {
    try {
      const response = await makeAuthenticatedRequest(`/api/patient-attachments?patient_id=${patientId}`);
      if (!response.ok) throw new Error('Failed to fetch attachments');
      const result = await response.json();
      const data = result.data || result;
      setAttachments(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching attachments:', error);
      setAttachments([]);
    } finally {
      setLoading(false);
    }
  };

  const handleSavePatient = async () => {
    try {
      const response = await makeAuthenticatedRequest(`/api/patients/${patientId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(editForm)
      });

      if (!response.ok) throw new Error('Failed to update patient');
      
      toast({
        title: "Sucesso!",
        description: "Dados do paciente atualizados.",
      });
      
      setEditMode(false);
      fetchPatientData();
    } catch (error) {
      console.error('Error updating patient:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar paciente.",
        variant: "destructive"
      });
    }
  };

  const handleFileUpload = async () => {
    if (!selectedFile) return;

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('patient_id', patientId);

      const response = await makeAuthenticatedRequest('/api/patient-attachments', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) throw new Error('Failed to upload file');
      
      toast({
        title: "Sucesso!",
        description: "Arquivo enviado com sucesso.",
      });
      
      setUploadDialogOpen(false);
      setSelectedFile(null);
      fetchAttachments();
    } catch (error) {
      console.error('Error uploading file:', error);
      toast({
        title: "Erro",
        description: "Erro ao enviar arquivo.",
        variant: "destructive"
      });
    }
  };

  const handleDeleteAttachment = async (attachmentId: string) => {
    if (!confirm('Tem certeza que deseja excluir este arquivo?')) return;

    try {
      const response = await makeAuthenticatedRequest(`/api/patient-attachments/${attachmentId}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete attachment');
      
      toast({
        title: "Sucesso!",
        description: "Arquivo excluído com sucesso.",
      });
      
      fetchAttachments();
    } catch (error) {
      console.error('Error deleting attachment:', error);
      toast({
        title: "Erro",
        description: "Erro ao excluir arquivo.",
        variant: "destructive"
      });
    }
  };

  const updateAppointmentStatus = async (appointmentId: string, status: string) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ status })
      });

      if (!response.ok) throw new Error('Failed to update appointment status');
      
      toast({
        title: "Sucesso!",
        description: "Status da consulta atualizado.",
      });
      
      fetchAppointments();
    } catch (error) {
      console.error('Error updating appointment status:', error);
      toast({
        title: "Erro",
        description: "Erro ao atualizar status da consulta.",
        variant: "destructive"
      });
    }
  };

  const handleDownloadAttachment = async (attachmentId: string, fileName: string) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/patient-attachments/${attachmentId}`);
      if (!response.ok) throw new Error('Failed to get download URL');

      const result = await response.json();
      const data = result.data || result;

      // Create a temporary link to download the file
      const link = document.createElement('a');
      link.href = data.download_url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading file:', error);
      toast({
        title: "Erro",
        description: "Erro ao baixar arquivo.",
        variant: "destructive"
      });
    }
  };

  const formatFileSize = (bytes: number | null): string => {
    if (!bytes) return 'Tamanho desconhecido';
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const calculateAge = (birthDate: string | null): string => {
    if (!birthDate) return 'Idade não informada';
    const today = new Date();
    const birth = new Date(birthDate);
    const age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();

    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      return `${age - 1} anos`;
    }
    return `${age} anos`;
  };

  const handleOpenMedicalRecord = async (appointment: Appointment) => {
    setSelectedAppointment(appointment);
    setMedicalRecordOpen(true);
    await fetchMedicalRecords(appointment.id);
  };

  const fetchMedicalRecords = async (appointmentId: string) => {
    try {
      const response = await makeAuthenticatedRequest(`/api/medical-records?appointment_id=${appointmentId}`);
      if (!response.ok) throw new Error('Failed to fetch medical records');
      const result = await response.json();
      const data = result.data || result;
      setMedicalRecords(Array.isArray(data) ? data : []);
    } catch (error) {
      console.error('Error fetching medical records:', error);
      setMedicalRecords([]);
    }
  };

  const handleAddMedicalRecord = async () => {
    if (!newRecord.trim() || !selectedAppointment) return;

    try {
      const response = await makeAuthenticatedRequest('/api/medical-records', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          appointment_id: selectedAppointment.id,
          patient_id: patientId,
          notes: newRecord.trim()
        })
      });

      if (!response.ok) throw new Error('Failed to add medical record');

      toast({
        title: "Sucesso!",
        description: "Registro médico adicionado.",
      });

      setNewRecord('');
      await fetchMedicalRecords(selectedAppointment.id);
    } catch (error) {
      console.error('Error adding medical record:', error);
      toast({
        title: "Erro",
        description: "Erro ao adicionar registro médico.",
        variant: "destructive"
      });
    }
  };

  const handleStartTreatment = async () => {
    if (!selectedAppointment) return;

    try {
      await updateAppointmentStatus(selectedAppointment.id, 'in_progress');
      toast({
        title: "Atendimento iniciado",
        description: "O status da consulta foi atualizado para 'Em Andamento'.",
      });

      // Refresh appointments to update the status
      await fetchAppointments();

      // Update the selected appointment status
      setSelectedAppointment({
        ...selectedAppointment,
        status: 'in_progress'
      });
    } catch (error) {
      console.error('Error starting treatment:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!patient) {
    return (
      <div className="text-center py-8">
        <p className="text-muted-foreground">Paciente não encontrado.</p>
        <Button onClick={() => router.back()} className="mt-4">
          Voltar
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{patient.name}</h1>
          <p className="text-muted-foreground">
            Paciente desde {formatDateBR(patient.created_at)}
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          {editMode ? (
            <>
              <Button onClick={handleSavePatient} size="sm">
                <Save className="mr-2 h-4 w-4" />
                Salvar
              </Button>
              <Button onClick={() => setEditMode(false)} variant="outline" size="sm">
                <X className="mr-2 h-4 w-4" />
                Cancelar
              </Button>
            </>
          ) : (
            <Button onClick={() => setEditMode(true)} size="sm">
              <Edit className="mr-2 h-4 w-4" />
              Editar
            </Button>
          )}
          <Button onClick={() => router.back()} variant="outline" size="sm">
            Voltar
          </Button>
        </div>
      </div>

      <Tabs defaultValue="dados" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="dados" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            Dados Principais
          </TabsTrigger>
          <TabsTrigger value="consultas" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Histórico de Consultas
          </TabsTrigger>
          <TabsTrigger value="anexos" className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Anexos
          </TabsTrigger>
        </TabsList>

        <TabsContent value="dados" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Informações Pessoais</CardTitle>
              <CardDescription>
                Dados básicos do paciente
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Nome Completo</Label>
                  {editMode ? (
                    <Input
                      id="name"
                      value={editForm.name}
                      onChange={(e) => setEditForm({ ...editForm, name: e.target.value })}
                    />
                  ) : (
                    <p className="text-sm">{patient.name}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="birth_date">Data de Nascimento</Label>
                  {editMode ? (
                    <Input
                      id="birth_date"
                      type="date"
                      value={editForm.birth_date}
                      onChange={(e) => setEditForm({ ...editForm, birth_date: e.target.value })}
                    />
                  ) : (
                    <p className="text-sm">
                      {patient.birth_date
                        ? `${formatDateBR(patient.birth_date)} (${calculateAge(patient.birth_date)})`
                        : 'Não informado'
                      }
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="cpf">CPF</Label>
                  {editMode ? (
                    <Input
                      id="cpf"
                      value={editForm.cpf}
                      onChange={(e) => setEditForm({ ...editForm, cpf: e.target.value })}
                    />
                  ) : (
                    <p className="text-sm">{patient.cpf || 'Não informado'}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="phone">Telefone</Label>
                  {editMode ? (
                    <Input
                      id="phone"
                      value={editForm.phone}
                      onChange={(e) => setEditForm({ ...editForm, phone: e.target.value })}
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">{patient.phone || 'Não informado'}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">E-mail</Label>
                  {editMode ? (
                    <Input
                      id="email"
                      type="email"
                      value={editForm.email}
                      onChange={(e) => setEditForm({ ...editForm, email: e.target.value })}
                    />
                  ) : (
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <p className="text-sm">{patient.email || 'Não informado'}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="address">Endereço</Label>
                  {editMode ? (
                    <Textarea
                      id="address"
                      value={editForm.address}
                      onChange={(e) => setEditForm({ ...editForm, address: e.target.value })}
                    />
                  ) : (
                    <div className="flex items-start gap-2">
                      <MapPin className="h-4 w-4 text-muted-foreground mt-0.5" />
                      <p className="text-sm">{patient.address || 'Não informado'}</p>
                    </div>
                  )}
                </div>

                <div className="space-y-2 md:col-span-2">
                  <Label htmlFor="notes">Observações</Label>
                  {editMode ? (
                    <Textarea
                      id="notes"
                      value={editForm.notes}
                      onChange={(e) => setEditForm({ ...editForm, notes: e.target.value })}
                      rows={3}
                    />
                  ) : (
                    <p className="text-sm">{patient.notes || 'Nenhuma observação'}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="consultas" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Histórico de Consultas</CardTitle>
              <CardDescription>
                {appointments.length} consulta(s) registrada(s)
              </CardDescription>
            </CardHeader>
            <CardContent>
              {appointments.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Calendar className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhuma consulta registrada para este paciente</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {appointments.map((appointment) => (
                    <div
                      key={appointment.id}
                      className="flex items-center justify-between p-4 rounded-lg border bg-card/50"
                    >
                      <div className="flex items-center space-x-4">
                        <div className="flex flex-col items-center">
                          <span className="text-sm font-medium">
                            {formatDateBR(appointment.start_time)}
                          </span>
                          <span className="text-xs text-muted-foreground">
                            {formatTimeBR(appointment.start_time)}
                          </span>
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium">{appointment.title}</h3>
                          <p className="text-sm text-muted-foreground">
                            {getAppointmentTypeBR(appointment.type)}
                          </p>
                          {appointment.healthcare_professional_name && (
                            <p className="text-xs text-muted-foreground">
                              {appointment.healthcare_professional_name}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={
                          appointment.status === 'in_progress' ? 'default' :
                          appointment.status === 'completed' ? 'secondary' :
                          appointment.status === 'cancelled' ? 'destructive' :
                          'outline'
                        }>
                          {getAppointmentStatusBR(appointment.status)}
                        </Badge>
                        {appointment.price && (
                          <span className="text-sm font-medium">
                            R$ {appointment.price.toFixed(2)}
                          </span>
                        )}
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleOpenMedicalRecord(appointment)}
                        >
                          <Stethoscope className="mr-2 h-4 w-4" />
                          Prontuário
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="anexos" className="space-y-6 mt-6">
          <Card>
            <CardHeader>
              <div className="flex justify-between items-center">
                <div>
                  <CardTitle>Anexos</CardTitle>
                  <CardDescription>
                    {attachments.length} arquivo(s) anexado(s)
                  </CardDescription>
                </div>
                <Dialog open={uploadDialogOpen} onOpenChange={setUploadDialogOpen}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="mr-2 h-4 w-4" />
                      Adicionar Arquivo
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Enviar Arquivo</DialogTitle>
                      <DialogDescription>
                        Selecione um arquivo para anexar ao prontuário do paciente
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="space-y-2">
                        <Label htmlFor="file">Arquivo</Label>
                        <Input
                          id="file"
                          type="file"
                          onChange={(e) => setSelectedFile(e.target.files?.[0] || null)}
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button variant="outline" onClick={() => setUploadDialogOpen(false)}>
                        Cancelar
                      </Button>
                      <Button onClick={handleFileUpload} disabled={!selectedFile}>
                        <Upload className="mr-2 h-4 w-4" />
                        Enviar
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
            </CardHeader>
            <CardContent>
              {attachments.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
                  <p>Nenhum arquivo anexado</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {attachments.map((attachment) => (
                    <div
                      key={attachment.id}
                      className="flex items-center justify-between p-4 rounded-lg border bg-card/50"
                    >
                      <div className="flex items-center space-x-4">
                        <FileText className="h-8 w-8 text-muted-foreground" />
                        <div>
                          <h3 className="font-medium">{attachment.file_name}</h3>
                          <p className="text-sm text-muted-foreground">
                            {formatFileSize(attachment.file_size)} • {formatDateTimeBR(attachment.created_at)}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDownloadAttachment(attachment.id, attachment.file_name)}
                        >
                          <Download className="mr-2 h-4 w-4" />
                          Baixar
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => handleDeleteAttachment(attachment.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Medical Record Dialog */}
      <Dialog open={medicalRecordOpen} onOpenChange={setMedicalRecordOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setMedicalRecordOpen(false)}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
              <div>
                <DialogTitle>Prontuário Médico</DialogTitle>
                <DialogDescription>
                  {selectedAppointment && (
                    <>
                      {selectedAppointment.title} - {formatDateTimeBR(selectedAppointment.start_time)}
                    </>
                  )}
                </DialogDescription>
              </div>
            </div>
          </DialogHeader>

          <div className="space-y-6">
            {/* Appointment Info */}
            {selectedAppointment && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Informações da Consulta</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Data</Label>
                      <p className="text-sm">{formatDateBR(selectedAppointment.start_time)}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Horário</Label>
                      <p className="text-sm">{formatTimeBR(selectedAppointment.start_time)} - {formatTimeBR(selectedAppointment.end_time)}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Tipo</Label>
                      <p className="text-sm">{getAppointmentTypeBR(selectedAppointment.type)}</p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Status</Label>
                      <Badge variant={
                        selectedAppointment.status === 'confirmed' ? 'default' :
                        selectedAppointment.status === 'completed' ? 'secondary' :
                        selectedAppointment.status === 'cancelled' ? 'destructive' :
                        selectedAppointment.status === 'in_progress' ? 'default' :
                        'outline'
                      }>
                        {getAppointmentStatusBR(selectedAppointment.status)}
                      </Badge>
                    </div>
                  </div>
                  {selectedAppointment.healthcare_professional_name && (
                    <div>
                      <Label className="text-sm font-medium">Profissional</Label>
                      <p className="text-sm">{selectedAppointment.healthcare_professional_name}</p>
                    </div>
                  )}
                  {selectedAppointment.description && (
                    <div>
                      <Label className="text-sm font-medium">Descrição</Label>
                      <p className="text-sm">{selectedAppointment.description}</p>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Start Treatment Button */}
            {selectedAppointment && selectedAppointment.status === 'confirmed' && (
              <Card>
                <CardContent className="pt-6">
                  <Button onClick={handleStartTreatment} className="w-full">
                    <Clock className="mr-2 h-4 w-4" />
                    Iniciar Atendimento
                  </Button>
                </CardContent>
              </Card>
            )}

            {/* Medical Records History */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Histórico de Registros</CardTitle>
                <CardDescription>
                  Registros médicos desta consulta
                </CardDescription>
              </CardHeader>
              <CardContent>
                {medicalRecords.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <FileText className="mx-auto h-12 w-12 mb-4 opacity-50" />
                    <p>Nenhum registro médico encontrado</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {medicalRecords.map((record, index) => (
                      <div key={index} className="border-l-4 border-primary pl-4 py-2">
                        <div className="flex justify-between items-start mb-2">
                          <span className="text-sm font-medium">
                            {formatDateTimeBR(record.created_at)}
                          </span>
                          <Badge variant="outline" className="text-xs">
                            {record.created_by_name || 'Sistema'}
                          </Badge>
                        </div>
                        <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                          {record.notes}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Add New Record */}
            {selectedAppointment && (selectedAppointment.status === 'in_progress' || selectedAppointment.status === 'confirmed') && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Adicionar Registro</CardTitle>
                  <CardDescription>
                    Adicione observações e anotações sobre esta consulta
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <Textarea
                    placeholder="Digite suas observações sobre a consulta..."
                    value={newRecord}
                    onChange={(e) => setNewRecord(e.target.value)}
                    rows={4}
                  />
                  <Button
                    onClick={handleAddMedicalRecord}
                    disabled={!newRecord.trim()}
                    className="w-full"
                  >
                    <Plus className="mr-2 h-4 w-4" />
                    Adicionar Registro
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default PatientDetailPage;
