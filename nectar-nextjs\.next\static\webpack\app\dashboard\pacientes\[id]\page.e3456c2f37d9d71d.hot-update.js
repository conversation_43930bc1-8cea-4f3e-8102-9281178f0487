"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/pacientes/[id]/page",{

/***/ "(app-pages-browser)/./src/app/dashboard/pacientes/[id]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/dashboard/pacientes/[id]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./src/components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./src/components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./src/components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/stethoscope.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,Clock,Download,Edit,FileText,Mail,MapPin,Phone,Plus,Save,Stethoscope,Trash2,Upload,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_date_utils__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/date-utils */ \"(app-pages-browser)/./src/lib/date-utils.ts\");\n/* harmony import */ var _lib_api_client__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/api-client */ \"(app-pages-browser)/./src/lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst PatientDetailPage = ()=>{\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast)();\n    const patientId = params.id;\n    const [patient, setPatient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [appointments, setAppointments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [attachments, setAttachments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [editMode, setEditMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadDialogOpen, setUploadDialogOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedFile, setSelectedFile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecordOpen, setMedicalRecordOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedAppointment, setSelectedAppointment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [medicalRecords, setMedicalRecords] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [newRecord, setNewRecord] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [editForm, setEditForm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        phone: '',\n        birth_date: '',\n        cpf: '',\n        address: '',\n        notes: ''\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PatientDetailPage.useEffect\": ()=>{\n            if (patientId) {\n                fetchPatientData();\n                fetchAppointments();\n                fetchAttachments();\n            }\n        }\n    }[\"PatientDetailPage.useEffect\"], [\n        patientId\n    ]);\n    const fetchPatientData = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch patient');\n            const result = await response.json();\n            const data = result.data || result;\n            setPatient(data);\n            setEditForm({\n                name: data.name || '',\n                email: data.email || '',\n                phone: data.phone || '',\n                birth_date: data.birth_date || '',\n                cpf: data.cpf || '',\n                address: data.address || '',\n                notes: data.notes || ''\n            });\n        } catch (error) {\n            console.error('Error fetching patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao carregar dados do paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const fetchAppointments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/appointments?patient_id=\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch appointments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAppointments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching appointments:', error);\n            setAppointments([]);\n        }\n    };\n    const fetchAttachments = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments?patient_id=\".concat(patientId));\n            if (!response.ok) throw new Error('Failed to fetch attachments');\n            const result = await response.json();\n            const data = result.data || result;\n            setAttachments(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching attachments:', error);\n            setAttachments([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleSavePatient = async ()=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patients/\".concat(patientId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(editForm)\n            });\n            if (!response.ok) throw new Error('Failed to update patient');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Dados do paciente atualizados.\"\n            });\n            setEditMode(false);\n            fetchPatientData();\n        } catch (error) {\n            console.error('Error updating patient:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar paciente.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleFileUpload = async ()=>{\n        if (!selectedFile) return;\n        try {\n            const formData = new FormData();\n            formData.append('file', selectedFile);\n            formData.append('patient_id', patientId);\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)('/api/patient-attachments', {\n                method: 'POST',\n                body: formData\n            });\n            if (!response.ok) throw new Error('Failed to upload file');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Arquivo enviado com sucesso.\"\n            });\n            setUploadDialogOpen(false);\n            setSelectedFile(null);\n            fetchAttachments();\n        } catch (error) {\n            console.error('Error uploading file:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao enviar arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteAttachment = async (attachmentId)=>{\n        if (!confirm('Tem certeza que deseja excluir este arquivo?')) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments/\".concat(attachmentId), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete attachment');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Arquivo excluído com sucesso.\"\n            });\n            fetchAttachments();\n        } catch (error) {\n            console.error('Error deleting attachment:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao excluir arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const updateAppointmentStatus = async (appointmentId, status)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/appointments/\".concat(appointmentId), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    status\n                })\n            });\n            if (!response.ok) throw new Error('Failed to update appointment status');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Status da consulta atualizado.\"\n            });\n            fetchAppointments();\n        } catch (error) {\n            console.error('Error updating appointment status:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao atualizar status da consulta.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDownloadAttachment = async (attachmentId, fileName)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/patient-attachments/\".concat(attachmentId));\n            if (!response.ok) throw new Error('Failed to get download URL');\n            const result = await response.json();\n            const data = result.data || result;\n            // Create a temporary link to download the file\n            const link = document.createElement('a');\n            link.href = data.download_url;\n            link.download = fileName;\n            document.body.appendChild(link);\n            link.click();\n            document.body.removeChild(link);\n        } catch (error) {\n            console.error('Error downloading file:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao baixar arquivo.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const formatFileSize = (bytes)=>{\n        if (!bytes) return 'Tamanho desconhecido';\n        const sizes = [\n            'Bytes',\n            'KB',\n            'MB',\n            'GB'\n        ];\n        const i = Math.floor(Math.log(bytes) / Math.log(1024));\n        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];\n    };\n    const calculateAge = (birthDate)=>{\n        if (!birthDate) return 'Idade não informada';\n        const today = new Date();\n        const birth = new Date(birthDate);\n        const age = today.getFullYear() - birth.getFullYear();\n        const monthDiff = today.getMonth() - birth.getMonth();\n        if (monthDiff < 0 || monthDiff === 0 && today.getDate() < birth.getDate()) {\n            return \"\".concat(age - 1, \" anos\");\n        }\n        return \"\".concat(age, \" anos\");\n    };\n    const handleOpenMedicalRecord = async (appointment)=>{\n        setSelectedAppointment(appointment);\n        setMedicalRecordOpen(true);\n        await fetchMedicalRecords(appointment.id);\n    };\n    const fetchMedicalRecords = async (appointmentId)=>{\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)(\"/api/medical-records?appointment_id=\".concat(appointmentId));\n            if (!response.ok) throw new Error('Failed to fetch medical records');\n            const result = await response.json();\n            const data = result.data || result;\n            setMedicalRecords(Array.isArray(data) ? data : []);\n        } catch (error) {\n            console.error('Error fetching medical records:', error);\n            setMedicalRecords([]);\n        }\n    };\n    const handleAddMedicalRecord = async ()=>{\n        if (!newRecord.trim() || !selectedAppointment) return;\n        try {\n            const response = await (0,_lib_api_client__WEBPACK_IMPORTED_MODULE_13__.makeAuthenticatedRequest)('/api/medical-records', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    appointment_id: selectedAppointment.id,\n                    patient_id: patientId,\n                    notes: newRecord.trim()\n                })\n            });\n            if (!response.ok) throw new Error('Failed to add medical record');\n            toast({\n                title: \"Sucesso!\",\n                description: \"Registro médico adicionado.\"\n            });\n            setNewRecord('');\n            await fetchMedicalRecords(selectedAppointment.id);\n        } catch (error) {\n            console.error('Error adding medical record:', error);\n            toast({\n                title: \"Erro\",\n                description: \"Erro ao adicionar registro médico.\",\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleStartTreatment = async ()=>{\n        if (!selectedAppointment) return;\n        try {\n            await updateAppointmentStatus(selectedAppointment.id, 'in_progress');\n            toast({\n                title: \"Atendimento iniciado\",\n                description: \"O status da consulta foi atualizado para 'Em Andamento'.\"\n            });\n            // Refresh appointments to update the status\n            await fetchAppointments();\n            // Update the selected appointment status\n            setSelectedAppointment({\n                ...selectedAppointment,\n                status: 'in_progress'\n            });\n        } catch (error) {\n            console.error('Error starting treatment:', error);\n        }\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-64\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-primary\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 396,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n            lineNumber: 395,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!patient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-muted-foreground\",\n                    children: \"Paciente n\\xe3o encontrado.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 404,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    onClick: ()=>router.back(),\n                    className: \"mt-4\",\n                    children: \"Voltar\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 405,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n            lineNumber: 403,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between items-start\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold tracking-tight\",\n                                children: patient.name\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 416,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-muted-foreground\",\n                                children: [\n                                    \"Paciente desde \",\n                                    (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(patient.created_at)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 417,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 415,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: handleSavePatient,\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 426,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Salvar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 425,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>setEditMode(false),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 430,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            \"Cancelar\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 429,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>setEditMode(true),\n                                size: \"sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                        className: \"mr-2 h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    \"Editar\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                onClick: ()=>router.back(),\n                                variant: \"outline\",\n                                size: \"sm\",\n                                children: \"Voltar\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 440,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 422,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 414,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                defaultValue: \"dados\",\n                className: \"w-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                        className: \"grid w-full grid-cols-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"dados\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 449,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Dados Principais\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 448,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"consultas\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 453,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Hist\\xf3rico de Consultas\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 452,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                value: \"anexos\",\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                        className: \"h-4 w-4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \"Anexos\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 447,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"dados\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Informa\\xe7\\xf5es Pessoais\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 465,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: \"Dados b\\xe1sicos do paciente\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 466,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 464,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    className: \"space-y-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"name\",\n                                                        children: \"Nome Completo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 473,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"name\",\n                                                        value: editForm.name,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                name: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 475,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 481,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"birth_date\",\n                                                        children: \"Data de Nascimento\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 486,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"birth_date\",\n                                                        type: \"date\",\n                                                        value: editForm.birth_date,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                birth_date: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.birth_date ? \"\".concat((0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(patient.birth_date), \" (\").concat(calculateAge(patient.birth_date), \")\") : 'Não informado'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 485,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"cpf\",\n                                                        children: \"CPF\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 505,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"cpf\",\n                                                        value: editForm.cpf,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                cpf: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.cpf || 'Não informado'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"phone\",\n                                                        children: \"Telefone\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 518,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"phone\",\n                                                        value: editForm.phone,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                phone: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 520,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 527,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.phone || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 528,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 526,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"email\",\n                                                        children: \"E-mail\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 534,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                        id: \"email\",\n                                                        type: \"email\",\n                                                        value: editForm.email,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                email: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 536,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 544,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.email || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 545,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 543,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 533,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"address\",\n                                                        children: \"Endere\\xe7o\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 551,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"address\",\n                                                        value: editForm.address,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                address: e.target.value\n                                                            })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 553,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4 text-muted-foreground mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 560,\n                                                                columnNumber: 23\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm\",\n                                                                children: patient.address || 'Não informado'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 561,\n                                                                columnNumber: 23\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 559,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 550,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2 md:col-span-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                        htmlFor: \"notes\",\n                                                        children: \"Observa\\xe7\\xf5es\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 567,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    editMode ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                        id: \"notes\",\n                                                        value: editForm.notes,\n                                                        onChange: (e)=>setEditForm({\n                                                                ...editForm,\n                                                                notes: e.target.value\n                                                            }),\n                                                        rows: 3\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 569,\n                                                        columnNumber: 21\n                                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm\",\n                                                        children: patient.notes || 'Nenhuma observação'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 576,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 566,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 470,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 463,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 462,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"consultas\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                            children: \"Hist\\xf3rico de Consultas\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 587,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                            children: [\n                                                appointments.length,\n                                                \" consulta(s) registrada(s)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 588,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 586,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: appointments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 595,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhuma consulta registrada para este paciente\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 596,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: appointments.map((appointment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex flex-col items-center\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(appointment.start_time)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 607,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(appointment.start_time)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 610,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 606,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium\",\n                                                                        children: appointment.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 615,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentTypeBR)(appointment.type)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 616,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    appointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs text-muted-foreground\",\n                                                                        children: appointment.healthcare_professional_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 620,\n                                                                        columnNumber: 29\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 614,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 605,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                variant: appointment.status === 'in_progress' ? 'default' : appointment.status === 'completed' ? 'secondary' : appointment.status === 'cancelled' ? 'destructive' : 'outline',\n                                                                children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentStatusBR)(appointment.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 627,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            appointment.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-sm font-medium\",\n                                                                children: [\n                                                                    \"R$ \",\n                                                                    appointment.price.toFixed(2)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 636,\n                                                                columnNumber: 27\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleOpenMedicalRecord(appointment),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 645,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"Prontu\\xe1rio\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 640,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 626,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, appointment.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 601,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 599,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 592,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 585,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 584,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                        value: \"anexos\",\n                        className: \"space-y-6 mt-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex justify-between items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                        children: \"Anexos\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 662,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                        children: [\n                                                            attachments.length,\n                                                            \" arquivo(s) anexado(s)\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 663,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 661,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                                                open: uploadDialogOpen,\n                                                onOpenChange: setUploadDialogOpen,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                            size: \"sm\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"mr-2 h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 670,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                \"Adicionar Arquivo\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 669,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 668,\n                                                        columnNumber: 19\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                                        children: \"Enviar Arquivo\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 676,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                                        children: \"Selecione um arquivo para anexar ao prontu\\xe1rio do paciente\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 677,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 675,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-4\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"space-y-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                            htmlFor: \"file\",\n                                                                            children: \"Arquivo\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 683,\n                                                                            columnNumber: 25\n                                                                        }, undefined),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_7__.Input, {\n                                                                            id: \"file\",\n                                                                            type: \"file\",\n                                                                            onChange: (e)=>{\n                                                                                var _e_target_files;\n                                                                                return setSelectedFile(((_e_target_files = e.target.files) === null || _e_target_files === void 0 ? void 0 : _e_target_files[0]) || null);\n                                                                            }\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 684,\n                                                                            columnNumber: 25\n                                                                        }, undefined)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 682,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 681,\n                                                                columnNumber: 21\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogFooter, {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        variant: \"outline\",\n                                                                        onClick: ()=>setUploadDialogOpen(false),\n                                                                        children: \"Cancelar\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 692,\n                                                                        columnNumber: 23\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                        onClick: handleFileUpload,\n                                                                        disabled: !selectedFile,\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                className: \"mr-2 h-4 w-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 696,\n                                                                                columnNumber: 25\n                                                                            }, undefined),\n                                                                            \"Enviar\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 695,\n                                                                        columnNumber: 23\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 691,\n                                                                columnNumber: 21\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 674,\n                                                        columnNumber: 19\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 667,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 660,\n                                        columnNumber: 15\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 659,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                    children: attachments.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center py-8 text-muted-foreground\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 707,\n                                                columnNumber: 19\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                children: \"Nenhum arquivo anexado\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 708,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 706,\n                                        columnNumber: 17\n                                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: attachments.map((attachment)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between p-4 rounded-lg border bg-card/50\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-8 w-8 text-muted-foreground\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 718,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                        className: \"font-medium\",\n                                                                        children: attachment.file_name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 720,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm text-muted-foreground\",\n                                                                        children: [\n                                                                            formatFileSize(attachment.file_size),\n                                                                            \" • \",\n                                                                            (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(attachment.created_at)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 721,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 719,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 717,\n                                                        columnNumber: 23\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDownloadAttachment(attachment.id, attachment.file_name),\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"mr-2 h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 732,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    \"Baixar\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 727,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"destructive\",\n                                                                onClick: ()=>handleDeleteAttachment(attachment.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 740,\n                                                                    columnNumber: 27\n                                                                }, undefined)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 735,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 726,\n                                                        columnNumber: 23\n                                                    }, undefined)\n                                                ]\n                                            }, attachment.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 713,\n                                                columnNumber: 21\n                                            }, undefined))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 711,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 704,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 658,\n                            columnNumber: 11\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                        lineNumber: 657,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 446,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.Dialog, {\n                open: medicalRecordOpen,\n                onOpenChange: setMedicalRecordOpen,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogContent, {\n                    className: \"max-w-4xl max-h-[90vh] overflow-y-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"sm\",\n                                        onClick: ()=>setMedicalRecordOpen(false),\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 762,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogTitle, {\n                                                children: \"Prontu\\xe1rio M\\xe9dico\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 765,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_10__.DialogDescription, {\n                                                children: selectedAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                    children: [\n                                                        selectedAppointment.title,\n                                                        \" - \",\n                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(selectedAppointment.start_time)\n                                                    ]\n                                                }, void 0, true)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 764,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                lineNumber: 756,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 755,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: [\n                                selectedAppointment && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                className: \"text-lg\",\n                                                children: \"Informa\\xe7\\xf5es da Consulta\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-2 gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Data\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 787,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateBR)(selectedAppointment.start_time)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 788,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 786,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Hor\\xe1rio\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 791,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: [\n                                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(selectedAppointment.start_time),\n                                                                        \" - \",\n                                                                        (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatTimeBR)(selectedAppointment.end_time)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 792,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 790,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Tipo\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 795,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm\",\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentTypeBR)(selectedAppointment.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 796,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 794,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                                    className: \"text-sm font-medium\",\n                                                                    children: \"Status\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 799,\n                                                                    columnNumber: 23\n                                                                }, undefined),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                    variant: selectedAppointment.status === 'confirmed' ? 'default' : selectedAppointment.status === 'completed' ? 'secondary' : selectedAppointment.status === 'cancelled' ? 'destructive' : selectedAppointment.status === 'in_progress' ? 'default' : 'outline',\n                                                                    children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.getAppointmentStatusBR)(selectedAppointment.status)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 800,\n                                                                    columnNumber: 23\n                                                                }, undefined)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 798,\n                                                            columnNumber: 21\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 785,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                selectedAppointment.healthcare_professional_name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Profissional\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 813,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedAppointment.healthcare_professional_name\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 814,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 812,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                selectedAppointment.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_8__.Label, {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"Descri\\xe7\\xe3o\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 819,\n                                                            columnNumber: 23\n                                                        }, undefined),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm\",\n                                                            children: selectedAppointment.description\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 820,\n                                                            columnNumber: 23\n                                                        }, undefined)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 818,\n                                                    columnNumber: 21\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 784,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 780,\n                                    columnNumber: 15\n                                }, undefined),\n                                selectedAppointment && selectedAppointment.status === 'confirmed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                        className: \"pt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                            onClick: handleStartTreatment,\n                                            className: \"w-full\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_29__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 832,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                \"Iniciar Atendimento\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 831,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 830,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 829,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Hist\\xf3rico de Registros\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 842,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Registros m\\xe9dicos desta consulta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 843,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 841,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: medicalRecords.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8 text-muted-foreground\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"mx-auto h-12 w-12 mb-4 opacity-50\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 850,\n                                                        columnNumber: 21\n                                                    }, undefined),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Nenhum registro m\\xe9dico encontrado\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 851,\n                                                        columnNumber: 21\n                                                    }, undefined)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 849,\n                                                columnNumber: 19\n                                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: medicalRecords.map((record, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"border-l-4 border-primary pl-4 py-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-start mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: (0,_lib_date_utils__WEBPACK_IMPORTED_MODULE_12__.formatDateTimeBR)(record.created_at)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 858,\n                                                                        columnNumber: 27\n                                                                    }, undefined),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_5__.Badge, {\n                                                                        variant: \"outline\",\n                                                                        className: \"text-xs\",\n                                                                        children: record.created_by_name || 'Sistema'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 861,\n                                                                        columnNumber: 27\n                                                                    }, undefined)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 857,\n                                                                columnNumber: 25\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-muted-foreground whitespace-pre-wrap\",\n                                                                children: record.notes\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 865,\n                                                                columnNumber: 25\n                                                            }, undefined)\n                                                        ]\n                                                    }, index, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 856,\n                                                        columnNumber: 23\n                                                    }, undefined))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 854,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 847,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 840,\n                                    columnNumber: 13\n                                }, undefined),\n                                selectedAppointment && (selectedAppointment.status === 'in_progress' || selectedAppointment.status === 'confirmed') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-lg\",\n                                                    children: \"Adicionar Registro\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 879,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    children: \"Adicione observa\\xe7\\xf5es e anota\\xe7\\xf5es sobre esta consulta\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 880,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 878,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_9__.Textarea, {\n                                                    placeholder: \"Digite suas observa\\xe7\\xf5es sobre a consulta...\",\n                                                    value: newRecord,\n                                                    onChange: (e)=>setNewRecord(e.target.value),\n                                                    rows: 4\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 885,\n                                                    columnNumber: 19\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    onClick: handleAddMedicalRecord,\n                                                    disabled: !newRecord.trim(),\n                                                    className: \"w-full\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_Clock_Download_Edit_FileText_Mail_MapPin_Phone_Plus_Save_Stethoscope_Trash2_Upload_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 896,\n                                                            columnNumber: 21\n                                                        }, undefined),\n                                                        \"Adicionar Registro\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 891,\n                                                    columnNumber: 19\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 884,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 877,\n                                    columnNumber: 15\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                            lineNumber: 777,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                    lineNumber: 754,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n                lineNumber: 753,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\next-js\\\\nectar\\\\nectar-nextjs\\\\src\\\\app\\\\dashboard\\\\pacientes\\\\[id]\\\\page.tsx\",\n        lineNumber: 413,\n        columnNumber: 5\n    }, undefined);\n};\n_s(PatientDetailPage, \"ZfrmVYD7nw2p5VZE5EbwWPcxtgU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_11__.useToast\n    ];\n});\n_c = PatientDetailPage;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PatientDetailPage);\nvar _c;\n$RefreshReg$(_c, \"PatientDetailPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/dashboard/pacientes/[id]/page.tsx\n"));

/***/ })

});