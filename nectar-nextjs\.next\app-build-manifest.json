{"pages": {"/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/css/app/layout.css", "static/chunks/app/layout.js"], "/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/page.js"], "/auth/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/auth/page.js"], "/api/user-roles/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/user-roles/route.js"], "/dashboard/layout": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/layout.js"], "/dashboard/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/page.js"], "/dashboard/agenda/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/agenda/page.js"], "/api/patients/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/patients/route.js"], "/api/healthcare-professionals/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/healthcare-professionals/route.js"], "/api/procedures/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/procedures/route.js"], "/api/appointments/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/appointments/route.js"], "/api/clinic-settings/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/clinic-settings/route.js"], "/dashboard/prontuario/[patientId]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/prontuario/[patientId]/page.js"], "/api/patients/[id]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/patients/[id]/route.js"], "/api/appointments/[id]/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/appointments/[id]/route.js"], "/api/medical-records/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/medical-records/route.js"], "/api/dashboard/stats/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/dashboard/stats/route.js"], "/dashboard/pacientes/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/pacientes/page.js"], "/dashboard/configuracoes/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/configuracoes/page.js"], "/api/settings/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/settings/route.js"], "/dashboard/pacientes/[id]/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/dashboard/pacientes/[id]/page.js"], "/api/patient-attachments/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/patient-attachments/route.js"], "/api/medical-records/cleanup-drafts/route": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/api/medical-records/cleanup-drafts/route.js"], "/_not-found/page": ["static/chunks/webpack.js", "static/chunks/main-app.js", "static/chunks/app/_not-found/page.js"]}}