"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/agenda/page",{

/***/ "(app-pages-browser)/./src/lib/validations.ts":
/*!********************************!*\
  !*** ./src/lib/validations.ts ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appointmentSchema: () => (/* binding */ appointmentSchema),\n/* harmony export */   clinicSettingsSchema: () => (/* binding */ clinicSettingsSchema),\n/* harmony export */   fileUploadSchema: () => (/* binding */ fileUploadSchema),\n/* harmony export */   formatCPF: () => (/* binding */ formatCPF),\n/* harmony export */   formatPhone: () => (/* binding */ formatPhone),\n/* harmony export */   healthcareProfessionalSchema: () => (/* binding */ healthcareProfessionalSchema),\n/* harmony export */   patientSchema: () => (/* binding */ patientSchema),\n/* harmony export */   procedureSchema: () => (/* binding */ procedureSchema),\n/* harmony export */   validateCPF: () => (/* binding */ validateCPF)\n/* harmony export */ });\n/* harmony import */ var zod__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zod */ \"(app-pages-browser)/./node_modules/zod/v3/types.js\");\n\n// Utility schemas\nconst phoneRegex = /^(\\+55\\s?)?(\\(?\\d{2}\\)?\\s?)?\\d{4,5}-?\\d{4}$/;\nconst cpfRegex = /^\\d{3}\\.\\d{3}\\.\\d{3}-\\d{2}$|^\\d{11}$/;\nconst crmRegex = /^\\d{4,6}\\/[A-Z]{2}$/;\n// Patient validation schema\nconst patientSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100, 'Nome deve ter no máximo 100 caracteres').regex(/^[a-zA-ZÀ-ÿ\\s]+$/, 'Nome deve conter apenas letras e espaços'),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email('E-mail inválido').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    birth_date: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional().refine((date)=>{\n        if (!date) return true;\n        const birthDate = new Date(date);\n        const today = new Date();\n        const age = today.getFullYear() - birthDate.getFullYear();\n        return age >= 0 && age <= 150;\n    }, 'Data de nascimento inválida'),\n    cpf: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(cpfRegex, 'CPF inválido. Use formato: 000.000.000-00').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    address: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(500, 'Endereço deve ter no máximo 500 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Observações devem ter no máximo 1000 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal(''))\n});\n// Healthcare Professional validation schema\nconst healthcareProfessionalSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(100, 'Nome deve ter no máximo 100 caracteres').regex(/^[a-zA-ZÀ-ÿ\\s]+$/, 'Nome deve conter apenas letras e espaços'),\n    specialty: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(100, 'Especialidade deve ter no máximo 100 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    crm: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(crmRegex, 'CRM inválido. Use formato: 123456/SP').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    phone: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(phoneRegex, 'Telefone inválido. Use formato: (11) 99999-9999').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    email: zod__WEBPACK_IMPORTED_MODULE_0__.string().email('E-mail inválido').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    is_active: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(true)\n});\n// Procedure validation schema\nconst procedureSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    name: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Nome deve ter pelo menos 2 caracteres').max(200, 'Nome deve ter no máximo 200 caracteres'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Descrição deve ter no máximo 1000 caracteres').optional().nullish().transform((val)=>val || ''),\n    default_price: zod__WEBPACK_IMPORTED_MODULE_0__.number().min(0, 'Preço deve ser maior ou igual a zero').max(99999.99, 'Preço deve ser menor que R$ 100.000,00').optional(),\n    duration_minutes: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Duração deve ser um número inteiro').min(5, 'Duração mínima é 5 minutos').max(480, 'Duração máxima é 8 horas').optional()\n});\n// Appointment validation schema\nconst appointmentSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    title: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(2, 'Título deve ter pelo menos 2 caracteres').max(200, 'Título deve ter no máximo 200 caracteres'),\n    description: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Descrição deve ter no máximo 1000 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    patient_id: zod__WEBPACK_IMPORTED_MODULE_0__.string().uuid('ID do paciente inválido'),\n    healthcare_professional_id: zod__WEBPACK_IMPORTED_MODULE_0__.string().uuid('ID do profissional inválido').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    start_time: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Data/hora de início é obrigatória').refine((val)=>{\n        console.log('[VALIDATION DEBUG] Validating start_time:', val);\n        // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format\n        const datetimeLocalRegex = /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}$/;\n        const isDatetimeLocal = datetimeLocalRegex.test(val);\n        const isValidDate = !isNaN(new Date(val).getTime());\n        console.log('[VALIDATION DEBUG] start_time validation:', {\n            val,\n            isDatetimeLocal,\n            isValidDate\n        });\n        return isDatetimeLocal || isValidDate;\n    }, 'Data/hora de início inválida'),\n    end_time: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Data/hora de fim é obrigatória').refine((val)=>{\n        console.log('[VALIDATION DEBUG] Validating end_time:', val);\n        // Accept both datetime-local format (YYYY-MM-DDTHH:mm) and ISO format\n        const datetimeLocalRegex = /^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}$/;\n        const isDatetimeLocal = datetimeLocalRegex.test(val);\n        const isValidDate = !isNaN(new Date(val).getTime());\n        console.log('[VALIDATION DEBUG] end_time validation:', {\n            val,\n            isDatetimeLocal,\n            isValidDate\n        });\n        return isDatetimeLocal || isValidDate;\n    }, 'Data/hora de fim inválida'),\n    type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'consultation',\n        'follow_up'\n    ], {\n        errorMap: ()=>({\n                message: 'Tipo de consulta inválido'\n            })\n    }),\n    status: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'scheduled',\n        'confirmed',\n        'in_progress',\n        'completed',\n        'cancelled'\n    ], {\n        errorMap: ()=>({\n                message: 'Status da consulta inválido'\n            })\n    }).optional(),\n    notes: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(1000, 'Observações devem ter no máximo 1000 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    // Recurrence fields\n    has_recurrence: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    recurrence_type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'daily',\n        'weekly',\n        'monthly'\n    ]).optional(),\n    recurrence_interval: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Intervalo deve ser um número inteiro').min(1, 'Intervalo mínimo é 1').max(12, 'Intervalo máximo é 12').optional(),\n    recurrence_days: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.number().int().min(1).max(7)).optional(),\n    recurrence_end_type: zod__WEBPACK_IMPORTED_MODULE_0__[\"enum\"]([\n        'never',\n        'date',\n        'count'\n    ]).optional(),\n    recurrence_end_date: zod__WEBPACK_IMPORTED_MODULE_0__.string().optional(),\n    recurrence_count: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Número de ocorrências deve ser um número inteiro').min(1, 'Mínimo 1 ocorrência').max(365, 'Máximo 365 ocorrências').optional()\n}).refine((data)=>{\n    // Validate that end_time is after start_time\n    try {\n        console.log('[DATE VALIDATION DEBUG] Validating dates:', {\n            start_time: data.start_time,\n            end_time: data.end_time,\n            start_time_type: typeof data.start_time,\n            end_time_type: typeof data.end_time\n        });\n        // Skip validation if either date is empty (will be caught by required validation)\n        if (!data.start_time || !data.end_time) {\n            console.log('[DATE VALIDATION DEBUG] One or both dates empty, skipping validation');\n            return true;\n        }\n        const start = new Date(data.start_time);\n        const end = new Date(data.end_time);\n        console.log('[DATE VALIDATION DEBUG] Parsed dates:', {\n            start: start.toISOString(),\n            end: end.toISOString(),\n            startTime: start.getTime(),\n            endTime: end.getTime(),\n            startValid: !isNaN(start.getTime()),\n            endValid: !isNaN(end.getTime())\n        });\n        // Check if dates are valid\n        if (isNaN(start.getTime()) || isNaN(end.getTime())) {\n            console.log('[DATE VALIDATION DEBUG] Invalid date(s) detected');\n            return false;\n        }\n        const isValid = end > start;\n        console.log('[DATE VALIDATION DEBUG] Comparison result:', {\n            endGreaterThanStart: isValid,\n            timeDifference: end.getTime() - start.getTime()\n        });\n        return isValid;\n    } catch (error) {\n        console.error('[DATE VALIDATION DEBUG] Date validation error:', error);\n        return false;\n    }\n}, {\n    message: 'Data/hora de fim deve ser posterior à data/hora de início',\n    path: [\n        'end_time'\n    ]\n}).refine((data)=>{\n    // Validate recurrence fields when has_recurrence is true\n    if (data.has_recurrence) {\n        return data.recurrence_type && data.recurrence_interval;\n    }\n    return true;\n}, {\n    message: 'Campos de recorrência são obrigatórios quando recorrência está ativada',\n    path: [\n        'recurrence_type'\n    ]\n}).refine((data)=>{\n    // Validate recurrence end date when end_type is 'date'\n    if (data.has_recurrence && data.recurrence_end_type === 'date') {\n        return data.recurrence_end_date;\n    }\n    return true;\n}, {\n    message: 'Data de fim é obrigatória quando tipo de fim é \"data\"',\n    path: [\n        'recurrence_end_date'\n    ]\n}).refine((data)=>{\n    // Validate recurrence count when end_type is 'count'\n    if (data.has_recurrence && data.recurrence_end_type === 'count') {\n        return data.recurrence_count && data.recurrence_count > 0;\n    }\n    return true;\n}, {\n    message: 'Número de ocorrências é obrigatório quando tipo de fim é \"contagem\"',\n    path: [\n        'recurrence_count'\n    ]\n});\n// Clinic Settings validation schema\nconst clinicSettingsSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    clinic_name: zod__WEBPACK_IMPORTED_MODULE_0__.string().max(200, 'Nome da clínica deve ter no máximo 200 caracteres').optional().or(zod__WEBPACK_IMPORTED_MODULE_0__.literal('')),\n    working_hours_start: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de início inválido'),\n    working_hours_end: zod__WEBPACK_IMPORTED_MODULE_0__.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Horário de fim inválido'),\n    working_days: zod__WEBPACK_IMPORTED_MODULE_0__.array(zod__WEBPACK_IMPORTED_MODULE_0__.number().int().min(1).max(7)).min(1, 'Pelo menos um dia de funcionamento deve ser selecionado'),\n    appointment_duration_minutes: zod__WEBPACK_IMPORTED_MODULE_0__.number().int('Duração deve ser um número inteiro').min(15, 'Duração mínima é 15 minutos').max(180, 'Duração máxima é 3 horas'),\n    allow_weekend_appointments: zod__WEBPACK_IMPORTED_MODULE_0__.boolean().default(false),\n    timezone: zod__WEBPACK_IMPORTED_MODULE_0__.string().min(1, 'Fuso horário é obrigatório')\n}).refine((data)=>{\n    // Validate that end time is after start time\n    const [startHour, startMinute] = data.working_hours_start.split(':').map(Number);\n    const [endHour, endMinute] = data.working_hours_end.split(':').map(Number);\n    const startMinutes = startHour * 60 + startMinute;\n    const endMinutes = endHour * 60 + endMinute;\n    return endMinutes > startMinutes;\n}, {\n    message: 'Horário de fim deve ser posterior ao horário de início',\n    path: [\n        'working_hours_end'\n    ]\n});\n// File upload validation\nconst fileUploadSchema = zod__WEBPACK_IMPORTED_MODULE_0__.object({\n    file: zod__WEBPACK_IMPORTED_MODULE_0__[\"instanceof\"](File).refine((file)=>file.size <= 50 * 1024 * 1024, 'Arquivo deve ter no máximo 50MB').refine((file)=>{\n        const allowedTypes = [\n            'image/jpeg',\n            'image/png',\n            'image/gif',\n            'application/pdf',\n            'text/plain',\n            'application/msword',\n            'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n        ];\n        return allowedTypes.includes(file.type);\n    }, 'Tipo de arquivo não permitido'),\n    patient_id: zod__WEBPACK_IMPORTED_MODULE_0__.string().uuid('ID do paciente inválido')\n});\n// Common validation utilities\nconst validateCPF = (cpf)=>{\n    // Remove formatting\n    const cleanCPF = cpf.replace(/[^\\d]/g, '');\n    if (cleanCPF.length !== 11) return false;\n    // Check for repeated digits\n    if (/^(\\d)\\1{10}$/.test(cleanCPF)) return false;\n    // Validate check digits\n    let sum = 0;\n    for(let i = 0; i < 9; i++){\n        sum += parseInt(cleanCPF.charAt(i)) * (10 - i);\n    }\n    let remainder = sum * 10 % 11;\n    if (remainder === 10 || remainder === 11) remainder = 0;\n    if (remainder !== parseInt(cleanCPF.charAt(9))) return false;\n    sum = 0;\n    for(let i = 0; i < 10; i++){\n        sum += parseInt(cleanCPF.charAt(i)) * (11 - i);\n    }\n    remainder = sum * 10 % 11;\n    if (remainder === 10 || remainder === 11) remainder = 0;\n    if (remainder !== parseInt(cleanCPF.charAt(10))) return false;\n    return true;\n};\nconst formatCPF = (cpf)=>{\n    const cleanCPF = cpf.replace(/[^\\d]/g, '');\n    return cleanCPF.replace(/(\\d{3})(\\d{3})(\\d{3})(\\d{2})/, '$1.$2.$3-$4');\n};\nconst formatPhone = (phone)=>{\n    const cleanPhone = phone.replace(/[^\\d]/g, '');\n    if (cleanPhone.length === 11) {\n        return cleanPhone.replace(/(\\d{2})(\\d{5})(\\d{4})/, '($1) $2-$3');\n    } else if (cleanPhone.length === 10) {\n        return cleanPhone.replace(/(\\d{2})(\\d{4})(\\d{4})/, '($1) $2-$3');\n    }\n    return phone;\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/validations.ts\n"));

/***/ })

});