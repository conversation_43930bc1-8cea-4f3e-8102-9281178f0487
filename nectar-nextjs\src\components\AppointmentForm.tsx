"use client"

import React, { useState, useEffect } from 'react';
import { useForm, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { DateTimeInput } from '@/components/ui/datetime-input';
import { Calendar, Clock, User, Stethoscope, Plus, Trash2, Search, AlertCircle } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { format, addMinutes } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { formatDateTimeForInput } from '@/lib/date-utils';
import { appointmentSchema, type AppointmentFormData } from '@/lib/validations';
import { useFormSubmission } from '@/hooks/useAsyncOperation';

// Utility functions for Brazilian timezone (UTC-3)
const brazilianTimezoneOffset = -3 * 60; // -3 hours in minutes

const toBrazilianTime = (date) => {
  if (!date) return null;
  
  // Se for string, converte para Date
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  
  // Cria uma nova data ajustada para UTC-3
  const utcTime = dateObj.getTime() + (dateObj.getTimezoneOffset() * 60000);
  const brazilianTime = new Date(utcTime + (brazilianTimezoneOffset * 60000));
  
  return brazilianTime;
};

const formatDateTimeForBrazilianInput = (dateTime) => {
  if (!dateTime) return '';
  
  try {
    const date = toBrazilianTime(dateTime);
    if (!date || isNaN(date.getTime())) return '';
    
    // Formato para datetime-local: YYYY-MM-DDTHH:mm
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch (error) {
    console.error('[DATETIME] Error formatting date:', error);
    return '';
  }
};

const parseBrazilianDateTime = (dateTimeString) => {
  if (!dateTimeString) return null;
  
  try {
    // Se já tem timezone, usa diretamente
    if (dateTimeString.includes('T') && (dateTimeString.includes('Z') || dateTimeString.includes('-03:00'))) {
      return new Date(dateTimeString);
    }
    
    // Se é formato datetime-local (YYYY-MM-DDTHH:mm), adiciona timezone brasileiro
    if (dateTimeString.includes('T')) {
      return new Date(dateTimeString + ':00-03:00');
    }
    
    // Fallback para outros formatos
    const date = new Date(dateTimeString);
    return toBrazilianTime(date);
  } catch (error) {
    console.error('[DATETIME] Error parsing date:', error);
    return null;
  }
};

type Patient = {
  id: string;
  name: string;
  email?: string;
  phone?: string;
};

type HealthcareProfessional = {
  id: string;
  name: string;
  specialty: string | null;
  is_active: boolean;
};

type Procedure = {
  id: string;
  name: string;
  description: string | null;
  default_price: number | null;
  duration_minutes: number | null;
};

type AppointmentProcedure = {
  procedure_id: string;
  procedure_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
};

interface AppointmentFormProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  patients: Patient[];
  healthcareProfessionals: HealthcareProfessional[];
  procedures: Procedure[];
  initialData?: Partial<AppointmentFormData & { id?: string }>;
  onSubmit: (data: AppointmentFormData & { procedures: AppointmentProcedure[]; id?: string }) => Promise<void>;
  loading?: boolean;
}

const AppointmentForm: React.FC<AppointmentFormProps> = ({
  open,
  onOpenChange,
  patients,
  healthcareProfessionals,
  procedures,
  initialData,
  onSubmit,
  loading = false
}) => {
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('scheduling');
  const [searchProcedure, setSearchProcedure] = useState('');
  const [selectedProcedures, setSelectedProcedures] = useState<AppointmentProcedure[]>([]);

  // Check if we're in editing mode
  const isEditing = Boolean(initialData?.id);

  const { execute: submitForm, loading: submitting } = useFormSubmission({
    successMessage: isEditing ? 'Consulta atualizada com sucesso!' : 'Consulta agendada com sucesso!',
    errorMessage: isEditing ? 'Erro ao atualizar consulta' : 'Erro ao agendar consulta'
  });

  const form = useForm<AppointmentFormData>({
    resolver: zodResolver(appointmentSchema),
    defaultValues: {
      title: '',
      description: '',
      patient_id: '',
      healthcare_professional_id: '',
      start_time: '',
      end_time: '',
      type: 'consultation',
      status: 'scheduled',
      notes: '',
      has_recurrence: false,
      recurrence_type: 'weekly',
      recurrence_interval: 1,
      recurrence_days: [],
      recurrence_end_type: 'never',
      recurrence_end_date: '',
      recurrence_count: 1,
    }
  });

  const { control, handleSubmit, watch, setValue, reset, formState: { errors } } = form;

  const watchedValues = watch();

  // Initialize form with initial data
  useEffect(() => {
    if (initialData && open) {
      console.log('[DATETIME] Initializing form with data:', initialData);
      
      const formattedData = {
        ...initialData,
        start_time: formatDateTimeForBrazilianInput(initialData.start_time),
        end_time: formatDateTimeForBrazilianInput(initialData.end_time),
        patient_id: initialData.patient_id || '',
        healthcare_professional_id: initialData.healthcare_professional_id || '',
      };
      
      console.log('[DATETIME] Formatted data for form:', formattedData);
      reset(formattedData);
    }
  }, [initialData, open, reset]);

  // Auto-generate title when patient and type change
  useEffect(() => {
    if (watchedValues.patient_id && watchedValues.type) {
      const patient = patients.find(p => p.id === watchedValues.patient_id);
      if (patient) {
        const typeLabel = watchedValues.type === 'consultation' ? 'Consulta' : 'Retorno';
        setValue('title', `${typeLabel} - ${patient.name}`);
      }
    }
  }, [watchedValues.patient_id, watchedValues.type, patients, setValue]);

  // Auto-calculate end time based on start time (Brazilian timezone)
  // Only auto-calculate if end_time is not already set (e.g., from FullCalendar selection)
  useEffect(() => {
    console.log('[DATETIME] Auto-calculating end time, start_time:', watchedValues.start_time, 'end_time:', watchedValues.end_time);

    if (watchedValues.start_time && !watchedValues.end_time) {
      try {
        const startTime = parseBrazilianDateTime(watchedValues.start_time);

        console.log('[DATETIME] Parsed start time:', startTime, 'Valid:', startTime && !isNaN(startTime.getTime()));

        if (startTime && !isNaN(startTime.getTime())) {
          const totalDuration = selectedProcedures.reduce((total, proc) => {
            const procedure = procedures.find(p => p.id === proc.procedure_id);
            return total + (procedure?.duration_minutes || 30) * proc.quantity;
          }, 30); // Default 30 minutes if no procedures

          console.log('[DATETIME] Total duration:', totalDuration, 'minutes');

          const endTime = addMinutes(startTime, totalDuration);
          const formattedEndTime = formatDateTimeForBrazilianInput(endTime);

          console.log('[DATETIME] Calculated end time:', endTime, 'Formatted:', formattedEndTime);
          setValue('end_time', formattedEndTime);
        }
      } catch (error) {
        console.error('[DATETIME] Error calculating end time:', error);
      }
    }
  }, [watchedValues.start_time, watchedValues.end_time, selectedProcedures, procedures, setValue]);

  const filteredProcedures = procedures.filter(procedure =>
    procedure.name.toLowerCase().includes(searchProcedure.toLowerCase()) ||
    (procedure.description && procedure.description.toLowerCase().includes(searchProcedure.toLowerCase()))
  );

  const addProcedure = (procedure: Procedure) => {
    const existingIndex = selectedProcedures.findIndex(p => p.procedure_id === procedure.id);
    
    if (existingIndex >= 0) {
      // Increase quantity if already exists
      const updated = [...selectedProcedures];
      updated[existingIndex].quantity += 1;
      updated[existingIndex].total_price = updated[existingIndex].quantity * updated[existingIndex].unit_price;
      setSelectedProcedures(updated);
    } else {
      // Add new procedure
      const newProcedure: AppointmentProcedure = {
        procedure_id: procedure.id,
        procedure_name: procedure.name,
        quantity: 1,
        unit_price: procedure.default_price || 0,
        total_price: procedure.default_price || 0,
      };
      setSelectedProcedures([...selectedProcedures, newProcedure]);
    }
  };

  const updateProcedure = (index: number, field: 'quantity' | 'unit_price', value: number) => {
    const updated = [...selectedProcedures];
    updated[index][field] = value;
    updated[index].total_price = updated[index].quantity * updated[index].unit_price;
    setSelectedProcedures(updated);
  };

  const removeProcedure = (index: number) => {
    setSelectedProcedures(selectedProcedures.filter((_, i) => i !== index));
  };

  const getTotalPrice = () => {
    return selectedProcedures.reduce((total, proc) => total + proc.total_price, 0);
  };

  const onFormSubmit = async (data: AppointmentFormData) => {
    console.log('[DATETIME] Form submitted with data:', data);
    
    try {
      // Parse and validate dates in Brazilian timezone
      const startTime = parseBrazilianDateTime(data.start_time);
      const endTime = parseBrazilianDateTime(data.end_time);
      
      if (!startTime || !endTime) {
        toast({
          title: 'Erro',
          description: 'Datas inválidas. Por favor, verifique os horários.',
          variant: 'destructive'
        });
        return;
      }
      
      // Convert to ISO string with Brazilian timezone
      const formattedData = {
        ...data,
        start_time: startTime.toISOString(),
        end_time: endTime.toISOString(),
      };
      
      console.log('[DATETIME] Formatted data for submission:', formattedData);

      await submitForm(async () => {
        const appointmentData = {
          ...formattedData,
          procedures: selectedProcedures,
          total_price: getTotalPrice(),
          status: formattedData.status || 'scheduled',
          ...(isEditing && initialData?.id && { id: initialData.id }),
        };

        console.log('[DATETIME] Final appointment data:', appointmentData);
        await onSubmit(appointmentData);

        // Reset form
        reset();
        setSelectedProcedures([]);
        setActiveTab('scheduling');
        onOpenChange(false);
      });
    } catch (error) {
      console.error('[DATETIME] Error submitting form:', error);
      toast({
        title: 'Erro',
        description: 'Erro ao processar os dados. Por favor, tente novamente.',
        variant: 'destructive'
      });
    }
  };

  const toggleRecurrenceDay = (day: number) => {
    const currentDays = watchedValues.recurrence_days || [];
    const newDays = currentDays.includes(day)
      ? currentDays.filter(d => d !== day)
      : [...currentDays, day];
    setValue('recurrence_days', newDays);
  };

  const weekDays = [
    { value: 1, label: 'D' }, // Domingo
    { value: 2, label: 'S' }, // Segunda
    { value: 3, label: 'T' }, // Terça
    { value: 4, label: 'Q' }, // Quarta
    { value: 5, label: 'Q' }, // Quinta
    { value: 6, label: 'S' }, // Sexta
    { value: 7, label: 'S' }, // Sábado
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>{isEditing ? 'Editar Consulta' : 'Agendar Nova Consulta'}</DialogTitle>
          <DialogDescription>
            {isEditing ? 'Edite os dados da consulta' : 'Preencha os dados para agendar uma nova consulta'}
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit(onFormSubmit)}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="scheduling" className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                Agendamento
              </TabsTrigger>
              <TabsTrigger value="procedures" className="flex items-center gap-2">
                <Stethoscope className="h-4 w-4" />
                Procedimentos
              </TabsTrigger>
            </TabsList>

            <TabsContent value="scheduling" className="space-y-4 mt-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="patient">Paciente *</Label>
                  <Controller
                    name="patient_id"
                    control={control}
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o paciente" />
                        </SelectTrigger>
                        <SelectContent>
                          {patients.map(patient => (
                            <SelectItem key={patient.id} value={patient.id}>
                              <div className="flex items-center gap-2">
                                <User className="h-4 w-4" />
                                {patient.name}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.patient_id && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.patient_id.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="professional">Profissional</Label>
                  <Controller
                    name="healthcare_professional_id"
                    control={control}
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue placeholder="Selecione o profissional" />
                        </SelectTrigger>
                        <SelectContent>
                          {healthcareProfessionals
                            .filter(prof => prof.is_active)
                            .map(professional => (
                              <SelectItem key={professional.id} value={professional.id}>
                                {professional.name}
                                {professional.specialty && ` - ${professional.specialty}`}
                              </SelectItem>
                            ))}
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.healthcare_professional_id && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.healthcare_professional_id.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="type">Tipo de Consulta *</Label>
                  <Controller
                    name="type"
                    control={control}
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="consultation">Consulta</SelectItem>
                          <SelectItem value="follow_up">Retorno</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.type && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.type.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="title">Título *</Label>
                  <Controller
                    name="title"
                    control={control}
                    render={({ field }) => (
                      <Input
                        id="title"
                        {...field}
                      />
                    )}
                  />
                  {errors.title && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.title.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="status">Status</Label>
                  <Controller
                    name="status"
                    control={control}
                    render={({ field }) => (
                      <Select value={field.value} onValueChange={field.onChange}>
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="scheduled">Agendado</SelectItem>
                          <SelectItem value="confirmed">Confirmado</SelectItem>
                          <SelectItem value="in_progress">Em Andamento</SelectItem>
                          <SelectItem value="completed">Concluído</SelectItem>
                          <SelectItem value="cancelled">Cancelado</SelectItem>
                        </SelectContent>
                      </Select>
                    )}
                  />
                  {errors.status && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.status.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  {/* Empty space for layout balance */}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_time">Data/Hora Início *</Label>
                  <Controller
                    name="start_time"
                    control={control}
                    render={({ field }) => (
                      <DateTimeInput
                        id="start_time"
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="DD/MM/AAAA HH:mm"
                      />
                    )}
                  />
                  {errors.start_time && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.start_time.message}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_time">Data/Hora Fim *</Label>
                  <Controller
                    name="end_time"
                    control={control}
                    render={({ field }) => (
                      <DateTimeInput
                        id="end_time"
                        value={field.value}
                        onChange={field.onChange}
                        placeholder="DD/MM/AAAA HH:mm"
                      />
                    )}
                  />
                  {errors.end_time && (
                    <p className="text-sm text-destructive flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.end_time.message}
                    </p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Descrição</Label>
                <Controller
                  name="description"
                  control={control}
                  render={({ field }) => (
                    <Textarea
                      id="description"
                      placeholder="Descrição da consulta..."
                      {...field}
                    />
                  )}
                />
                {errors.description && (
                  <p className="text-sm text-destructive flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.description.message}
                  </p>
                )}
              </div>

              {/* Recurrence Section */}
              <Card>
                <CardHeader>
                  <div className="flex items-center space-x-2">
                    <Controller
                      name="has_recurrence"
                      control={control}
                      render={({ field }) => (
                        <Checkbox
                          id="has_recurrence"
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      )}
                    />
                    <Label htmlFor="has_recurrence" className="text-sm font-medium">
                      Recorrência Personalizada
                    </Label>
                  </div>
                </CardHeader>

                {watchedValues.has_recurrence && (
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <Label>Repetir a cada:</Label>
                        <div className="flex items-center gap-2">
                          <Controller
                            name="recurrence_interval"
                            control={control}
                            render={({ field }) => (
                              <Input
                                type="number"
                                min="1"
                                {...field}
                                onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                className="w-20"
                              />
                            )}
                          />
                          <Controller
                            name="recurrence_type"
                            control={control}
                            render={({ field }) => (
                              <Select value={field.value} onValueChange={field.onChange}>
                                <SelectTrigger>
                                  <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="daily">dia(s)</SelectItem>
                                  <SelectItem value="weekly">semana(s)</SelectItem>
                                  <SelectItem value="monthly">mês(es)</SelectItem>
                                </SelectContent>
                              </Select>
                            )}
                          />
                        </div>
                      </div>

                      {watchedValues.recurrence_type === 'weekly' && (
                        <div className="space-y-2">
                          <Label>Repetir:</Label>
                          <div className="flex gap-1">
                            {weekDays.map(day => (
                              <Button
                                key={day.value}
                                type="button"
                                variant={(watchedValues.recurrence_days || []).includes(day.value) ? 'default' : 'outline'}
                                size="sm"
                                className="w-8 h-8 p-0"
                                onClick={() => toggleRecurrenceDay(day.value)}
                              >
                                {day.label}
                              </Button>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="space-y-2">
                      <Label>Termina em:</Label>
                      <Controller
                        name="recurrence_end_type"
                        control={control}
                        render={({ field }) => (
                          <RadioGroup value={field.value} onValueChange={field.onChange}>

                        <div className="flex items-center space-x-2">
                          <RadioGroupItem value="never" id="never" />
                          <Label htmlFor="never">Nunca</Label>
                        </div>
                        
                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="date" id="end_date" />
                              <Label htmlFor="end_date">Em</Label>
                              <Controller
                                name="recurrence_end_date"
                                control={control}
                                render={({ field }) => (
                                  <Input
                                    type="date"
                                    {...field}
                                    disabled={watchedValues.recurrence_end_type !== 'date'}
                                    className="w-40"
                                  />
                                )}
                              />
                            </div>

                            <div className="flex items-center space-x-2">
                              <RadioGroupItem value="count" id="end_count" />
                              <Label htmlFor="end_count">Após</Label>
                              <Controller
                                name="recurrence_count"
                                control={control}
                                render={({ field }) => (
                                  <Input
                                    type="number"
                                    min="1"
                                    {...field}
                                    onChange={(e) => field.onChange(parseInt(e.target.value) || 1)}
                                    disabled={watchedValues.recurrence_end_type !== 'count'}
                                    className="w-20"
                                  />
                                )}
                              />
                              <Label>ocorrências</Label>
                            </div>
                          </RadioGroup>
                        )}
                      />
                    </div>
                  </CardContent>
                )}
              </Card>
            </TabsContent>

            <TabsContent value="procedures" className="space-y-4 mt-4">
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Buscar procedimentos..."
                    value={searchProcedure}
                    onChange={(e) => setSearchProcedure(e.target.value)}
                    className="flex-1"
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-60 overflow-y-auto">
                  {filteredProcedures.map(procedure => (
                    <Card key={procedure.id} className="cursor-pointer hover:bg-accent/50" onClick={() => addProcedure(procedure)}>
                      <CardContent className="p-3">
                        <div className="flex justify-between items-start">
                          <div className="flex-1">
                            <h4 className="font-medium text-sm">{procedure.name}</h4>
                            {procedure.description && (
                              <p className="text-xs text-muted-foreground mt-1">{procedure.description}</p>
                            )}
                            <div className="flex items-center gap-2 mt-2">
                              {procedure.default_price && (
                                <Badge variant="secondary" className="text-xs">
                                  R$ {procedure.default_price.toFixed(2)}
                                </Badge>
                              )}
                              {procedure.duration_minutes && (
                                <Badge variant="outline" className="text-xs">
                                  {procedure.duration_minutes}min
                                </Badge>
                              )}
                            </div>
                          </div>
                          <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
                {/* Selected Procedures */}
                {selectedProcedures.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">Procedimentos Selecionados</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      {selectedProcedures.map((proc, index) => (
                        <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                          <div className="flex-1">
                            <h4 className="font-medium">{proc.procedure_name}</h4>
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Label className="text-xs">Qtd:</Label>
                            <Input
                              type="number"
                              min="1"
                              value={proc.quantity}
                              onChange={(e) => updateProcedure(index, 'quantity', parseInt(e.target.value) || 1)}
                              className="w-16 h-8"
                            />
                          </div>
                          
                          <div className="flex items-center gap-2">
                            <Label className="text-xs">Preço:</Label>
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              value={proc.unit_price}
                              onChange={(e) => updateProcedure(index, 'unit_price', parseFloat(e.target.value) || 0)}
                              className="w-24 h-8"
                            />
                          </div>
                          
                          <div className="text-sm font-medium">
                            R$ {proc.total_price.toFixed(2)}
                          </div>
                          
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeProcedure(index)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                      
                      <div className="flex justify-between items-center pt-3 border-t">
                        <span className="font-medium">Total:</span>
                        <span className="text-lg font-bold">R$ {getTotalPrice().toFixed(2)}</span>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
          </Tabs>

          <DialogFooter className="mt-6">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancelar
            </Button>
            <Button type="submit" disabled={submitting || loading}>
              {submitting
                ? (isEditing ? 'Salvando...' : 'Agendando...')
                : (isEditing ? 'Salvar Alterações' : 'Agendar Consulta')
              }
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default AppointmentForm;
